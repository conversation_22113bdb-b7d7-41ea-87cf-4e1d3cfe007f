package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import com.carplus.subscribe.db.mysql.dao.SkuShipmentHistoryRepository;
import com.carplus.subscribe.db.mysql.dao.SkuShipmentRepository;
import com.carplus.subscribe.db.mysql.entity.contract.*;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.enums.ShipmentStatus;
import com.carplus.subscribe.event.MerchandisePriceInfoCreatedEvent;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.shipment.SkuShipmentCriteria;
import com.carplus.subscribe.model.shipment.SkuShipmentDetailResponse;
import com.carplus.subscribe.model.shipment.SkuShipmentResponse;
import com.carplus.subscribe.server.AuthorityServer;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SkuShipmentService {

    @Autowired
    private SkuShipmentRepository skuShipmentRepository;
    @Autowired
    private SkuShipmentHistoryRepository shipmentHistoryRepository;
    @Autowired
    private PriceInfoService priceInfoService;
    @Autowired
    private SkuService skuService;
    @Autowired
    private AuthorityServer authorityServer;
    @Autowired
    private OrderService orderService;

    /**
     * Get shipment list with pagination
     */
    public Page<SkuShipmentResponse> getShipmentList(SkuShipmentCriteria criteria, PageRequest pageRequest) {
        // First get the total count of all records matching the criteria
        long totalCount = skuShipmentRepository.count(criteria);

        if (totalCount == 0) {
            return Page.of(0, Collections.emptyList(), pageRequest.getSkip(), pageRequest.getLimit());
        }

        List<SkuShipmentResponse> skuShipmentResponseList = skuShipmentRepository.search(criteria, pageRequest.getSkip(), pageRequest.getLimit());

        List<String> orderNos = skuShipmentResponseList.stream()
            .map(SkuShipmentResponse::getOrderNo)
            .collect(Collectors.toList());
        Map<Integer, OrderPriceInfo> refOrderPriceInfoMap = priceInfoService.getUnPaidPriceInfoByOrders(orderNos, true)
            .stream()
            .filter(orderPriceInfo -> orderPriceInfo.getRefPriceInfoNo() != null)
            .collect(Collectors.toMap(OrderPriceInfo::getRefPriceInfoNo, Function.identity()));

        Map<String, Sku> skuMap = skuService.getMapByCodes(skuShipmentResponseList.stream()
            .map(SkuShipmentResponse::getSkuList)
            .flatMap(List::stream)
            .map(SkuShipmentResponse.SkuOrderPriceInfo::getSkuCode)
            .collect(Collectors.toSet()));

        Set<String> memberIds = skuShipmentResponseList.stream()
            .map(SkuShipmentResponse::getSkuList)
            .flatMap(List::stream)
            .map(SkuShipmentResponse.SkuOrderPriceInfo::getSkuShipmentList)
            .flatMap(List::stream)
            .map(SkuShipmentResponse.ShipmentInfo::getCreator)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        Map<String, MemberInfo> memberInfoMap = authorityServer.getMemberInfosByMemberIds(memberIds);

        List<SkuShipmentResponse> responseList = skuShipmentResponseList.stream()
            .peek(shipment -> shipment.getSkuList().forEach(skuOrderPriceInfo -> {
                skuOrderPriceInfo.setSkuName(skuMap.get(skuOrderPriceInfo.getSkuCode()).getName());
                Integer negativeAmount = Optional.ofNullable(refOrderPriceInfoMap.get(skuOrderPriceInfo.getOrderPriceInfoId()))
                    .map(OrderPriceInfo::getActualPrice).orElse(0);
                skuOrderPriceInfo.setAmount(skuOrderPriceInfo.getAmount() + negativeAmount);
                skuOrderPriceInfo.getSkuShipmentList().forEach(shipmentInfo ->
                    shipmentInfo.setCreatorName(Optional.ofNullable(memberInfoMap.get(shipmentInfo.getCreator()))
                        .map(MemberInfo::getMemberName).orElse(null)));
            }))
            .collect(Collectors.toList());

        return Page.of(
            totalCount,
            responseList,
            pageRequest.getSkip(),
            pageRequest.getLimit()
        );
    }

    /**
     * Get single shipment details
     */
    public SkuShipmentDetailResponse getShipmentDetail(Integer shipmentId) {
        SkuShipment shipment = skuShipmentRepository.findById(shipmentId)
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.SHIPMENT_NOT_FOUND));

        return convertToShipmentDetailResponse(shipment);
    }

    /**
     * 共用邏輯：為汽車用品費用清單建立出貨資料和歷程
     *
     * @param merchandisePriceInfos 汽車用品費用清單
     * @param memberId              建立者工號
     */
    private void createShipmentsForPriceInfos(Orders order, List<OrderPriceInfo> merchandisePriceInfos, String memberId) {
        if (CollectionUtils.isEmpty(merchandisePriceInfos)) {
            return;
        }
        if (!OrderStatus.of(order.getStatus()).isSkuShipmentAvailable()) {
            // 訂單狀態不適用於建立出貨資料
            throw new SubscribeException(SubscribeHttpExceptionCode.ORDER_STATUS_NOT_APPLICABLE_FOR_CREATING_SHIPMENT);
        }

        List<SkuShipment> shipments = new ArrayList<>();
        List<SkuShipmentHistory> shipmentHistories = new ArrayList<>();

        // 獨立處理每個 OrderPriceInfo，不合併相同 skuCode 的記錄
        for (OrderPriceInfo priceInfo : merchandisePriceInfos) {
            // 使用 orderPriceInfoId 作為主要參考，確保每筆汽車用品費用獨立處理
            Integer priceInfoId = priceInfo.getId();

            // 檢查已存在的出貨記錄數量，僅計算與當前 orderPriceInfoId 關聯的記錄
            int existingShipments = skuShipmentRepository.countByOrderPriceInfoId(priceInfoId);
            int requestedQuantity = priceInfo.getInfoDetail().getQuantity();
            int remainingQuantity = Math.max(0, requestedQuantity - existingShipments);

            // 只為差異數量建立新的出貨記錄，每個記錄都與特定的 orderPriceInfoId 關聯
            for (int i = 0; i < remainingQuantity; i++) {
                SkuShipment shipment = SkuShipment.builder()
                    .orderNo(priceInfo.getOrderNo())
                    .orderPriceInfoId(priceInfoId)
                    .skuCode(priceInfo.getSkuCode())
                    .status(ShipmentStatus.PENDING)
                    .creator(memberId)
                    .build();

                shipments.add(shipment);
            }
        }

        // 如果沒有需要建立的出貨記錄，直接返回
        if (shipments.isEmpty()) {
            return;
        }

        // 批量新增出貨資料
        List<SkuShipment> savedShipments = skuShipmentRepository.saveAll(shipments);

        // 為所有出貨資料建立初始歷程記錄
        for (SkuShipment shipment : savedShipments) {
            SkuShipmentHistory history = SkuShipmentHistory.builder()
                .shipmentId(shipment.getId())
                .status(ShipmentStatus.PENDING)
                .operator(memberId)
                .build();

            shipmentHistories.add(history);
        }

        // 批量新增出貨歷程記錄
        shipmentHistoryRepository.saveAll(shipmentHistories);
    }

    /**
     * 為訂單建立出貨資料
     */
    public void createShipments(String orderNo, String memberId) {
        Orders order = orderService.getOrder(orderNo);
        createShipments(order, memberId);
    }

    /**
     * 為訂單建立出貨資料
     */
    public void createShipments(Orders order, String memberId) {
        List<OrderPriceInfo> merchandisePriceInfos = priceInfoService.getPriceInfosByOrder(
            order.getOrderNo(),
            PriceInfoDefinition.PriceInfoCategory.Merchandise,
            PriceInfoDefinition.PriceInfoType.Pay
        );

        createShipmentsForPriceInfos(order, merchandisePriceInfos, memberId);
    }

    /**
     * 更新出貨狀態
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateShipmentStatus(Integer shipmentId, ShipmentStatus newStatus, String memberId) {
        SkuShipment shipment = skuShipmentRepository.findById(shipmentId)
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.SHIPMENT_NOT_FOUND));

        // 狀態沒改變，則不更新
        if (shipment.getStatus() == newStatus) {
            return;
        }

        shipment.setStatus(newStatus);
        skuShipmentRepository.save(shipment);

        // 新增出貨歷程
        SkuShipmentHistory history = SkuShipmentHistory.builder()
            .shipmentId(shipment.getId())
            .status(newStatus)
            .operator(memberId)
            .build();

        shipmentHistoryRepository.save(history);
    }

    private SkuShipmentDetailResponse convertToShipmentDetailResponse(SkuShipment shipment) {

        Orders order = shipment.getOrderPriceInfo().getOrder();

        SkuShipmentDetailResponse response = new SkuShipmentDetailResponse();
        response.setOrderNo(order.getOrderNo());
        response.setOrderStatus(order.getStatus());
        response.setOrderStartDate(Optional.ofNullable(order.getStartDate()).orElse(order.getExpectStartDate()));
        response.setOrderPriceInfoId(shipment.getOrderPriceInfoId());

        Map<Integer, OrderPriceInfo> refOrderPriceInfoMap = priceInfoService.getUnPaidPriceInfoByOrder(order.getOrderNo(), true)
            .stream()
            .filter(orderPriceInfo -> orderPriceInfo.getRefPriceInfoNo() != null)
            .collect(Collectors.toMap(OrderPriceInfo::getRefPriceInfoNo, Function.identity()));
        response.setAmount(shipment.getOrderPriceInfo().getAmount() + Optional.ofNullable(refOrderPriceInfoMap.get(shipment.getOrderPriceInfoId()))
            .map(OrderPriceInfo::getActualPrice).orElse(0));

        List<SkuShipmentHistory> shipmentHistoryList = shipment.getHistoryList();

        Set<String> memberIds = shipmentHistoryList.stream()
            .map(SkuShipmentHistory::getOperator)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        Map<String, MemberInfo> memberInfoMap = authorityServer.getMemberInfosByMemberIds(memberIds);

        List<SkuShipmentDetailResponse.ShipmentHistoryInfo> historyList = shipmentHistoryList
            .stream()
            .map(history -> {
                SkuShipmentDetailResponse.ShipmentHistoryInfo historyInfo = new SkuShipmentDetailResponse.ShipmentHistoryInfo();
                historyInfo.setId(history.getId());
                historyInfo.setStatus(history.getStatus());
                historyInfo.setStatusName(history.getStatus().getDescription());
                historyInfo.setUpdateDate(history.getInstantCreateDate());
                historyInfo.setOperator(history.getOperator());
                Optional.ofNullable(historyInfo.getOperator())
                    .ifPresent(memberId -> historyInfo.setOperatorName(memberInfoMap.get(memberId).getMemberName()));
                return historyInfo;
            })
            .collect(Collectors.toList());
        response.setHistoryList(historyList);
        
        return response;
    }

    /**
     * 處理汽車用品費用建立事件，自動建立出貨資料及歷程
     * 每個 OrderPriceInfo 獨立處理，即使多個記錄共享相同的 skuCode 和 orderNo
     */
    @Async
    @TransactionalEventListener
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void handleMerchandisePriceInfoCreated(MerchandisePriceInfoCreatedEvent event) {
        createShipmentsForPriceInfos(event.getOrder(), event.getMerchandisePriceInfos(), event.getMemberId());
    }
}
