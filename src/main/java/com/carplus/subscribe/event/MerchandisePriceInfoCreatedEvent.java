package com.carplus.subscribe.event;

import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

@Getter
public class MerchandisePriceInfoCreatedEvent extends ApplicationEvent {

    private final Orders order;
    private final List<OrderPriceInfo> merchandisePriceInfos;
    private final String memberId;

    public MerchandisePriceInfoCreatedEvent(Object source, Orders order, List<OrderPriceInfo> merchandisePriceInfos, String memberId) {
        super(source);
        this.order = order;
        this.merchandisePriceInfos = merchandisePriceInfos;
        this.memberId = memberId;
    }
}