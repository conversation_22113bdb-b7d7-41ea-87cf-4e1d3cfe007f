package com.carplus.subscribe.event;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class OrderClosedEvent extends ApplicationEvent {

    private final Orders order;
    private final String closeRemark;
    private final String memberId;

    public OrderClosedEvent(Object source, Orders order, String closeRemark, String memberId) {
        super(source);
        this.order = order;
        this.closeRemark = closeRemark;
        this.memberId = memberId;
    }
}
