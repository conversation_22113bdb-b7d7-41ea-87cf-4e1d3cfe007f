package com.carplus.subscribe.service;

import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.dao.ContractRepository;
import com.carplus.subscribe.db.mysql.dao.MainContractRepository;
import com.carplus.subscribe.db.mysql.dao.SubscribeLevelRepository;
import com.carplus.subscribe.db.mysql.dto.SubscribeCalendarDto;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.event.OrderBookingEvent;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.PriceInfo;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.crs.PurchaseProjectCarSearchResponse;
import com.carplus.subscribe.model.invoice.Invoice;
import com.carplus.subscribe.model.lrental.ContractCustomerCreateRequest;
import com.carplus.subscribe.model.region.City;
import com.carplus.subscribe.model.request.contract.*;
import com.carplus.subscribe.model.request.priceinfo.MileageDiscountRequest;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.server.GoSmartServer;
import com.carplus.subscribe.server.SrentalServer;
import com.carplus.subscribe.server.cars.LrentalServer;
import com.carplus.subscribe.utils.DateUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Service
public class ContractLogic {

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private MainContractRepository mainContractRepository;

    @Autowired
    private SubscribeLevelRepository subscribeLevelRepository;

    @Autowired
    private CarsService carsService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private CrsService crsService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private AuthServer authServer;

    @Autowired
    private GoSmartServer goSmartServer;

    @Autowired
    private LrentalServer lrentalServer;

    @Autowired
    private ContractService contractService;

    @Autowired
    private StationService stationService;

    @Autowired
    private NotifyService notifyService;

    @Autowired
    private NotifyToCService notifyToCService;

    @Autowired
    private SubscribeCalendarService subscribeCalendarService;

    @Autowired
    private RentalTaskService rentalTaskService;
    @Autowired
    private SrentalServer srentalServer;
    @Autowired
    private EContractService eContractService;
    @Autowired
    private AuthorityServer authorityServer;
    @Autowired
    private SubscribeLevelService subscribeLevelService;
    @Autowired
    private SkuService skuService;
    @Autowired
    private CarWishlistService carWishlistService;
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    public void contractCreateRequestValidate(ContractCreateReq req, boolean fromInternal) {
        Stations stations = stationService.findByStationCode(req.getDepartStationCode());
        SubscribeLevel subscribeLevel = subscribeLevelRepository.findByLevel(req.getCarLevel());
        if (stations == null) {
            throw new SubscribeException(STATION_NOT_FOUND);
        }
        if (req.getCompanyDriver() != null
            &&
            (StringUtils.isBlank(req.getCompanyDriver().getCompanyName())
                || StringUtils.isBlank(req.getCompanyDriver().getCompanyLocation())
                || StringUtils.isBlank(req.getCompanyDriver().getVatNumber()))
            &&
            !(StringUtils.isBlank(req.getCompanyDriver().getCompanyName())
                && StringUtils.isBlank(req.getCompanyDriver().getCompanyLocation())
                && StringUtils.isBlank(req.getCompanyDriver().getVatNumber()))
        ) {
            throw new SubscribeException(COMPANY_DRIVER_ERROR);
        }
        // 取車時間檢查，若為續約，自動計算取車時間
        if (StringUtils.isBlank(req.getMainContractNo())) {
            if (req.getExpectStartDate() == null) {
                throw new SubscribeException(EXPECT_DEPART_DATE_NOT_FOUND);
            }
            Cars car = carsService.findByPlateNo(req.getPlateNo());
            if (!theDateCanDepart(car.getCarNo(), new Date(req.getExpectStartDate().toEpochMilli()), fromInternal, stations.getStationCode())) {
                if (fromInternal) {
                    throw new SubscribeException(DEPART_TIME_CONSTRAINT_INTERNAL);
                } else {
                    if (car.getPrepWorkdays() != null) {
                        throw new SubscribeException(HttpStatus.OK, SPECIFIED_PREPARE_DAYS_CONSTRAINT,
                            String.format(SPECIFIED_PREPARE_DAYS_CONSTRAINT.getMsg(), car.getPrepWorkdays()));
                    } else {
                        throw new SubscribeException((CarDefine.CarState.NEW == car.getCarState()) ? NEW_CAR_DEPART_TIME_CONSTRAINT : OLD_CAR_DEPART_TIME_CONSTRAINT);
                    }
                }
            }
            if (req.getInvoice() == null) {
                req.setInvoice(Invoice.defaultInvoice());
            }
            if (!stations.checkBusinessTime(new Date(req.getExpectStartDate().toEpochMilli()))) {
                throw new SubscribeException(STATION_DEPART_TIME_CONSTRAINT);
            }
            if (subscribeLevel.getType().getMonths() > req.getMonth()) {
                throw new SubscribeException(LESS_THAN_MUST_RENT_DAYS);
            }
        } else {
            // 取母單還車時間
            MainContract mainContract = Optional.ofNullable(mainContractRepository.getMainContractByNo(req.getMainContractNo(), true)).orElseThrow(() -> new SubscribeException(MAIN_CONTRACT_NOT_FOUND));
            Orders order = mainContract.getContracts().stream().map(contract ->
                contract.getOrders().stream().filter(orders ->
                    orders.getStatus() == OrderStatus.DEPART.getStatus()).findAny().orElse(null)).filter(Objects::nonNull).findAny().orElseThrow(() -> new SubscribeException(ORDER_STATUS_NOT_BOOKING_NOT_DEPART));
            // 檢查續約時間是否早於原約預計還車時間
            if (order.getExpectEndDate().atZone(DateUtils.ZONE_TPE).toLocalDate().atStartOfDay().isAfter(req.getExpectStartDate().atZone(DateUtils.ZONE_TPE).toLocalDate().atStartOfDay())) {
                throw new SubscribeException(ORDER_RENEW_START_DATE_BEFORE_MAIN_CONTRACT_EXPECT_END_DAY);
            }
            req.setExpectStartDate(order.getExpectEndDate().plus(1, ChronoUnit.DAYS));
        }
        // 驗證汽車用品 merchList 中每個商品是否存在
        if (CollectionUtils.isNotEmpty(req.getMerchList())) {
            Set<String> skuCodeSet = req.getMerchList().stream().map(MerchandiseReq::getSkuCode).collect(Collectors.toSet());
            skuService.validateAllCodesExist(skuCodeSet);
        }
    }

    /**
     * 建立合約
     */
    public Contract createContract(ContractCreateReq req, Integer acctId, @Nullable String memberId,
                                   boolean needAutoCredit, @Nullable String autoCreditBypassReason) {
        boolean isInternal = req instanceof InternalContractCreateReq;
        List<City> cities = goSmartServer.getCityArea();

        // validate
        AuthUser user = authServer.getUserWithRetry(acctId);
        // 黑名單檢查
        if (!authServer.checkAcctBlackList(Lists.newArrayList(acctId)).isEmpty()) {
            throw new SubscribeException(BACK_LIST);
        }
        Cars car = carsService.findByPlateNo(req.getPlateNo());
        if (Objects.isNull(car)) {
            throw new SubscribeException(CAR_NOT_FOUND);
        }
        // 續約不檢查方案
        if (StringUtils.isBlank(req.getMainContractNo()) && !Objects.equals(car.getSubscribeLevel(), req.getCarLevel())) {
            throw new SubscribeException(SUBSCRIBE_LEVEL_CHANGED);
        }

        // 檢查車籍
        // 若為官網初次建單，則需車籍為上架狀態才可見建立訂單
        // 其他則不為車損或不使用皆可建立
        if (!isInternal && StringUtils.isBlank(req.getMainContractNo())) {
            if (!car.getLaunched().equals(CarDefine.Launched.open)) {
                throw new SubscribeException(CAR_LAUNCHED_NOT_OPEN);
            }
        } else {
            if (car.getLaunched().equals(CarDefine.Launched.deprecate)) {
                throw new SubscribeException(CAR_LAUNCHED_DEPRECATE);
            }
            if (car.getLaunched() == CarDefine.Launched.accident) {
                throw new SubscribeException(CAR_LAUNCHED_ACCIDENT);
            }
            if (car.getLaunched() == CarDefine.Launched.tbc) {
                throw new SubscribeException(CAR_LAUNCHED_TBC);
            }
        }

        if (StringUtils.isBlank(req.getMainContractNo()) && !car.getCarStatus().equals(CarDefine.CarStatus.Free.getCode())) {
            throw new SubscribeException(NOT_EMPTY_CAR);
        }

        carsService.updateCrsCarNo(car);
        SubscribeLevel initialSubscribeLevel = subscribeLevelService.findByLevel(req.getCarLevel());
        SubscribeLevel adoptedSubscribeLevel = subscribeLevelService.determineSubscribeLevel(car, initialSubscribeLevel);

        Contract contract = generateContract(req, car, user, adoptedSubscribeLevel);

        MemberInfo memberInfo = authorityServer.getMemberInfos(memberId).stream().findFirst().orElse(null);

        // 建立訂單、合約
        contract = contractService.createContractAndOrders(contract, req, acctId, memberInfo);
        contract = contractService.getContractAndOrdersByContractNo(contract.getContractNo());

        Orders order = contract.getOrders().stream().filter(o -> o.getStatus() == OrderStatus.CREDIT_PENDING.getStatus()).findAny().orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));
        // 額外里程數折扣
        if (req.getMileageDiscounts() != null && !req.getMileageDiscounts().isEmpty()) {
            orderService.updateMileageDiscounts(order, new MileageDiscountRequest(req.getMileageDiscounts()));
        }
        orderService.recordRenewTypeToPreOrder(order.getOrderNo(), RenewType.RENEW);
        orderService.updateOrder(order);

        boolean inWishlist = carWishlistService.isCarInWishlist(acctId, car.getPlateNo());
        String remark = buildOriginalOrderCarRemark(car, inWishlist);
        orderService.addRemark(order, remark, memberInfo);

        // 判斷是否需要進行自動授信
        if (!needAutoCredit) {
            // 處理不需要自動授信的情況
            orderService.handleAutoCreditBypass(order, memberId, autoCreditBypassReason);
        } else {
            // 正常進行自動授信
            orderService.autoCredit(order, user, true, memberId);
        }

        if (adoptedSubscribeLevel.getSecurityDeposit() == 0 && OrderStatus.CREDITED.getStatus() == order.getStatus()) {
            order.setStatus(OrderStatus.BOOKING.getStatus());
            eventPublisher.publishEvent(new OrderBookingEvent(this, order, memberId));
            if (order.getIsNewOrder()) {
                MainContract mainContract = order.getContract().getMainContract();
                mainContract.getOriginalPriceInfo().getSecurityDepositInfo().setSecurityDepositDate(new Date());
                // 鎖車
                Cars mainCar = carsService.findByPlateNo(mainContract.getPlateNo());
                orderService.lockCarAfterOrderPay(order, mainCar);
                mainContractRepository.save(mainContract);
                // 寄送通知
                notifyService.notifyPaySecurityDepositSuccess(order, user);
                notifyToCService.notifySecurityDepositPaid(order, user);
            }
        }
        // 專案車檢查
        if (car.getCrsCarNo() != null) {
            PurchaseProjectCarSearchResponse purchaseProjectCarSearchResponse = crsService.searchProjectCar(car.getCrsCarNo());
            if (purchaseProjectCarSearchResponse != null && Objects.equals(purchaseProjectCarSearchResponse.getIsProjectCar(), Boolean.TRUE)) {
                try {
                    purchaseProjectCarSearchResponse.validate(order.getExpectEndDate());
                } catch (SubscribeException e) {
                    orderService.addRemark(order, car.getPlateNo() + e.getMessage(), memberInfo);
                }
            }
        }
        orderService.checkIsUnpaid(order);
        orderService.recordSrentalInfo(order);
        orderService.updateOrder(order);

        if (OrderStatus.CREDITED.getStatus() == order.getStatus()) {
            ContractCustomerCreateRequest request = new ContractCustomerCreateRequest(order, user, cities, configService.getLRentalConfig());
            lrentalServer.createUserToDA71(request, memberId);
        }

        return contract;
    }

    /**
     * 組合原始收訂車輛備註
     */
    private String buildOriginalOrderCarRemark(Cars car, boolean inWishlist) {
        List<String> extras = Stream.of(
            Optional.ofNullable(car.getCnDesc())
                .filter(StringUtils::isNotBlank)
                .map(desc -> "車體描述: " + desc)
                .orElse(null),
            inWishlist ? "有加入用戶收藏清單" : null
        ).filter(Objects::nonNull).collect(Collectors.toList());

        return "原始收訂車輛: " + car.getPlateNo() + (extras.isEmpty() ? "" : "(" + String.join("；", extras) + ")");
    }

    /**
     * 訂單續約
     */
    public Orders renewOrder(String orderNo, OrderRenewRequest renewRequest, boolean isInternal, String memberId, boolean needAutoCredit, String autoCreditBypassReason) {
        ContractCreateReq req = isInternal ? new InternalContractCreateReq() : new ContractCreateReq();
        Orders orders = orderService.getUserOrder(orderNo, renewRequest.getAcctId());
        Contract contract = orders.getContract();
        MainContract mainContract = mainContractRepository.getMainContractByNo(contract.getMainContractNo(), true);
        isRenewable(mainContract);
        req.setPlateNo(mainContract.getPlateNo());
        req.setCarLevel(mainContract.getOriginalPriceInfo().isLevelDiscounted()
            ? carsService.findByPlateNo(mainContract.getPlateNo()).getSubscribeLevel()
            : mainContract.getGivenCarLevel());
        req.setDepartStationCode(mainContract.getDepartStationCode());
        req.setExpectStartDate(orders.getExpectEndDate().plus(1, ChronoUnit.DAYS));
        req.setMonth(renewRequest.getMonth().getValue());
        req.setOrderPlatform(renewRequest.getOrderPlatform());
        req.setMainContractNo(mainContract.getMainContractNo());
        req.setDisclaimer(contract.getDisclaimer());
        req.setPremium(contract.getPremium());
        req.setReplacement(contract.getReplacement());
        req.setDriver(mainContract.getDriver());
        req.setReturnStationCode(mainContract.getReturnStationCode());
        req.setInvoice(orders.getInvoice());
        req.setCustSource(mainContract.getCustSource());
        req.setCompanyDriver(mainContract.getCompanyDriver());
        req.setCarReady(mainContract.getCarReady());
        req.setReferInfo(mainContract.getReferInfo());
        req.setMerchList(renewRequest.getMerchList());
        contractCreateRequestValidate(req, false); // 不管為 true 或 false 都不影響結果
        Contract renewContract = createContract(req, renewRequest.getAcctId(), memberId, needAutoCredit, autoCreditBypassReason);
        Orders renewOrder = renewContract.getOrders().stream().sorted(Comparator.comparing(Orders::getCreateDate).reversed()).collect(Collectors.toList()).get(0);
        // 刪除還車點車任務
        rentalTaskService.deleteTask(orders, TaskType.RETURN);
        // 續約建立合約
        orderService.createLrentalContractAfterRenew(memberId, orders, renewOrder);
        return renewOrder;
    }

    public CalculateRequest prepareRenewalPriceRequest(String orderNo, CalculateOrderRenewRequest req) {
        // 建立計價用的 request
        CalculateRequest calculateRequest = new CalculateRequest();

        // 取得原訂單資訊
        Orders orders = orderService.getUserOrder(orderNo, req.getAcctId());
        Contract contract = orders.getContract();
        MainContract mainContract = mainContractRepository.getMainContractByNo(contract.getMainContractNo(), true);

        // 檢查是否可續約
        isRenewable(mainContract);

        // 設定計價參數
        calculateRequest.setPlateNo(mainContract.getPlateNo());
        calculateRequest.setCarLevel(mainContract.getOriginalPriceInfo().isLevelDiscounted()
            ? carsService.findByPlateNo(mainContract.getPlateNo()).getSubscribeLevel()
            : mainContract.getGivenCarLevel());
        calculateRequest.setDepartStationCode(mainContract.getDepartStationCode());
        calculateRequest.setExpectStartDate(orders.getExpectEndDate());
        calculateRequest.setMonth(SubscribeMonth.fromValue(req.getMonth().getValue()));
        calculateRequest.setMainContractNo(mainContract.getMainContractNo());
        calculateRequest.setDisclaimer(contract.getDisclaimer());
        calculateRequest.setPremium(contract.getPremium());
        calculateRequest.setReplacement(contract.getReplacement());

        return calculateRequest;
    }

    /**
     * 組合產生Contract資料
     */
    public Contract generateContract(ContractCreateReq req, Cars car, AuthUser user, SubscribeLevel adoptedSubscribeLevel) {
        Contract contract = new Contract();
        MainContract mainContract;

        // 是否新約
        if (StringUtils.isBlank(req.getMainContractNo())) {
            contract.setStage(1);
            contract.setContractNo(contractService.generateContractNo());
            contract.setExpectStartDate(req.getExpectStartDate());
            contract.setPremium(req.isPremium());
            contract.setDisclaimer(req.isDisclaimer());
            contract.setReplacement(req.isReplacement());
            contract.setStatus(ContractStatus.CREATE.getCode());
            contract.setContractNo(contractService.generateContractNo());
            contract.setEContractTempVerId(req.getEContractTempVerId());
            mainContract = new MainContract();
            mainContract.setExpectStartDate(req.getExpectStartDate());
            mainContract.setMainContractNo(contractService.generateMContractNo());
            mainContract.setStatus(ContractStatus.CREATE.getCode());
            mainContract.setOriginalPriceInfo(new PriceInfo(adoptedSubscribeLevel, req.getMonth()));
            mainContract.setDepartStationCode(req.getDepartStationCode());
            mainContract.setReturnStationCode(req.getReturnStationCode());
            mainContract.setCompanyDriver(req.getCompanyDriver());
            mainContract.setCustSource(req.getCustSource());
            mainContract.setPlateNo(car.getPlateNo());
            mainContract.setOrderPlateNo(car.getPlateNo());
            mainContract.setCarReady(req.getCarReady());
            mainContract.setReferInfo(req.getReferInfo());
            mainContract.setRemark(req.getRemark());
            mainContract.getOriginalPriceInfo().setCarState(car.getCarState());
            // 是否為優惠月費車
            mainContract.getOriginalPriceInfo().setMonthlyDiscounted(car.isMonthlyDiscounted());
            // 是否啟用超激優惠
            mainContract.getOriginalPriceInfo().setLevelDiscounted(adoptedSubscribeLevel.isDiscountLevelEnabled());
        } else {
            mainContract = Optional.ofNullable(mainContractRepository.getMainContractByNo(req.getMainContractNo(), true)).orElseThrow(() -> new SubscribeException(MAIN_CONTRACT_NOT_FOUND));
            if (!isRenewable(mainContract)) {
                throw new SubscribeException(MAIN_CONTRACT_CAN_NOT_RENEW);
            }

            contract = generateContractStage(car, mainContract, req);

        }

        Instant expectEndDate = Optional.ofNullable(contract.getStartDate()).orElse(contract.getExpectStartDate()).atZone(DateUtils.ZONE_TPE).plusMonths(12).minusDays(1).toInstant();
        mainContract.setAcctId(user.getAcctId());
        mainContract.setIdNo(user.getLoginId());
        mainContract.setCarModelCode(car.getCarModelCode());
        mainContract.setGivenCarLevel(adoptedSubscribeLevel.getLevel());
        mainContract.setDriver(req.getDriver());
        mainContract.setExpectEndDate(expectEndDate);

        contract.setExpectEndDate(expectEndDate);
        contract.setMainContractNo(mainContract.getMainContractNo());
        contract.setMainContract(mainContract);
        return contract;
    }

    /**
     * 是否可續約
     */
    public boolean isRenewable(String mainContractNo, int acctId) {
        MainContract mainContract = mainContractRepository.getMainContractByAcctId(acctId, mainContractNo);
        mainContract = mainContractRepository.getMainContractByNo(mainContract.getMainContractNo(), true);
        if (!isRenewable(mainContract)) {
            throw new SubscribeException(MAIN_CONTRACT_CAN_NOT_RENEW);
        }
        return true;
    }

    /**
     * 是否可續約
     */
    public boolean isRenewable(String mainContractNo) {
        MainContract mainContract = Optional.ofNullable(mainContractRepository.getMainContractByNo(mainContractNo, true)).orElseThrow(() -> new SubscribeException(MAIN_CONTRACT_NOT_FOUND));
        if (!isRenewable(mainContract)) {
            throw new SubscribeException(MAIN_CONTRACT_CAN_NOT_RENEW);
        }
        return true;
    }

    /**
     * 是否可續約
     */
    public boolean isRenewable(MainContract mainContract) {
        Contract lastContract = mainContract.getContracts().stream().max(Comparator.comparing(Contract::getStage)).orElseThrow(() -> new SubscribeException(CONTRACT_IS_EMPTY));
        // 訂單必須為出車中
        Orders orders = contractService.getContractAndOrdersByContractNo(lastContract.getContractNo()).getOrders().stream()
            .filter(o -> o.getStatus() == OrderStatus.DEPART.getStatus()).findAny()
            .orElseThrow(() -> new SubscribeException(RENEW_DEPART_ORDER_NOT_FOUND));
        if (RenewType.RENEW.equals(orders.getRenewType()) || Objects.equals(RenewType.AUTO_RENEW, orders.getRenewType())) {
            throw new SubscribeException(RENEW_ORDER_ALREADY_EXIST);
        }
        if (DateUtil.convertToStartOfInstant(orders.getExpectEndDate().minus(31, ChronoUnit.DAYS)).isAfter(DateUtil.convertToStartOfInstant(Instant.now()))) {
            throw new SubscribeException(ORDER_RENEW_START_DATE_NOT_THE_TIME_YET);
        }
        return true;
    }

    public RenewableType publicIsRenewable(MainContract mainContract, int acctId) {
        Boolean isBlackList = !authServer.checkAcctBlackList(Lists.newArrayList(acctId)).isEmpty();
        return publicIsRenewable(mainContract, isBlackList);
    }

    public RenewableType publicIsRenewable(MainContract mainContract, Boolean isBlackList) {
        RenewableType isRenewable = publicIsRenewable(mainContract);
        if (isRenewable == RenewableType.RENEWABLE && (isBlackList == null || isBlackList)) {
            return RenewableType.NON_RENEWABLE;
        }
        return isRenewable;
    }

    private RenewableType publicIsRenewable(MainContract mainContract) {
        RenewableType isRenewable = RenewableType.NON_RENEWABLE;
        Contract lastContract = mainContract.getContracts().stream().max(Comparator.comparing(Contract::getStage)).orElse(null);
        if (lastContract == null) {
            return isRenewable;
        }
        Orders orders = contractService.getContractAndOrdersByContractNo(lastContract.getContractNo()).getOrders().stream()
            .filter(o -> o.getStatus() == OrderStatus.DEPART.getStatus()).findAny()
            .orElse(null);
        if (orders == null) {
            return RenewableType.NONE;
        }
        isRenewable = publicIsRenewable(orders);
        return isRenewable;
    }

    public RenewableType publicIsRenewable(Orders orders, Integer acctId) {
        Boolean isBlackList = !authServer.checkAcctBlackList(Lists.newArrayList(acctId)).isEmpty();
        return publicIsRenewable(orders, isBlackList);
    }

    public RenewableType publicIsRenewable(Orders orders, Boolean isBlackList) {
        RenewableType isRenewable = publicIsRenewable(orders);
        if (isRenewable == RenewableType.RENEWABLE && (isBlackList == null || isBlackList)) {
            return RenewableType.NON_RENEWABLE;
        }
        return isRenewable;
    }

    private RenewableType publicIsRenewable(Orders orders) {
        // 訂單必須為出車中
        if (orders.getStatus() != OrderStatus.DEPART.getStatus()
            || Objects.equals(orders.getRenewType(), RenewType.RENEW)
            || DateUtil.convertToStartOfInstant(orders.getExpectEndDate().minus(31, ChronoUnit.DAYS)).isAfter(DateUtil.convertToStartOfInstant(Instant.now()))) {
            return RenewableType.NONE;
        }
        CarResponse carResponse = carsService.getCarInfo(orders.getContract().getMainContract().getPlateNo());
        /* 訂單狀態 ≠ 50 出車中 or
         * 訂單租期 < 3 個月 or
         * 訂單續約狀態 = 完成續約 or
         * 訂單 nextStageOrder is not null or
         * 客戶是格上黑名單 or
         * 車輛 launched not in (open,close)
         */
        boolean isRenewable = orders.getStatus() == OrderStatus.DEPART.getStatus()
            && orders.getMonth() >= 3
            && !Objects.equals(orders.getRenewType(), RenewType.RENEW)
            && StringUtils.isBlank(orders.getNextStageOrderNo())
            && carResponse.getLaunched() != null
            && !carResponse.getLaunched().equals(CarDefine.Launched.deprecate)
            && !carResponse.getLaunched().equals(CarDefine.Launched.accident);
        return isRenewable ? RenewableType.RENEWABLE : RenewableType.NON_RENEWABLE;
    }

    /**
     * 產生合約與其期數
     */
    public Contract generateContractStage(Cars car, MainContract mainContract, ContractCreateReq req) {
        Contract preContract = contractRepository.getMaxStageContract(mainContract.getMainContractNo());
        Integer month = contractRepository.getContractOrders(preContract.getContractNo()).getOrders().stream().filter(order -> order.getStage() > 0).mapToInt(Orders::getMonth).sum();
        month += req.getMonth();
        // 若合約底下訂單月份+續約月費超過12個月，則須建立新合約
        if (month <= 12) {
            if (preContract.getStatus() == ContractStatus.COMPLETE.getCode()) {
                throw new SubscribeException(CONTRACT_COMPLETE_CAN_NOT_RENEW);
            }
            preContract = contractService.getContractAndOrdersByContractNo(preContract.getContractNo());
            return preContract;
        } else {
            Contract contract = new Contract();
            contract.setStage(preContract.getStage() + 1);
            contract.setParentContractNo(preContract.getContractNo());
            contract.setStatus(ContractStatus.CREATE.getCode());
            contract.setExpectStartDate(req.getExpectStartDate());
            contract.setContractNo(contractService.generateContractNo());
            // 續約依照新條件取得符合之合約範本
            Integer latestTemplateId = eContractService.getTemplateContractIdByCarNo(car.getCarNo(), EContractSource.CARPLUS, req.getMonth(), preContract.getDisclaimer());
            contract.setEContractTempVerId(Optional.ofNullable(latestTemplateId).map(Objects::toString).orElse(null));
            contract.setPremium(preContract.getPremium());
            contract.setDisclaimer(preContract.getDisclaimer());
            contract.setReplacement(preContract.getReplacement());
            return contract;
        }
    }

    /**
     * 檢查預計出車日是否可以出車
     *
     * @param carNo       車輛編號
     * @param departDate  預計出車日
     * @param stationCode 站所代碼
     */
    public boolean theDateCanDepart(String carNo, Date departDate, boolean fromInternal, String stationCode) {
        Date minWorkDate = getMinWorkDate(fromInternal, carNo);
        String departDateStr = DateUtils.toDateString(departDate, "yyyyMMdd");
        List<String> subscribeUnAvailableDate = new ArrayList<>();
        if (!fromInternal) {
            subscribeUnAvailableDate = subscribeCalendarService.getUnavailableCalendarList(new Date()).stream()
                .filter(subscribeCalendar -> subscribeCalendar.getStationCodes().isEmpty() || subscribeCalendar.getStationCodes().contains(stationCode))
                .map(SubscribeCalendarDto::getDate).collect(Collectors.toList());
        }
        return departDateStr.compareTo(DateUtils.toDateString(minWorkDate, "yyyyMMdd")) >= 0
            && !subscribeUnAvailableDate.contains(departDateStr);
    }

    /**
     * 取得可預約日期
     *
     * @param carNo       車輛編號
     * @param size        取得日期數量
     * @param stationCode 站所代碼
     */
    public List<Date> getSubscribeAvailableDate(String carNo, int size, String stationCode) {
        List<Date> availableDates = new ArrayList<>();
        int count = 0;
        int maxCount = 50;
        boolean isInternal = false;
        Date minWorkDate = getMinWorkDate(isInternal, carNo);
        minWorkDate = DateUtil.convertToStartOfInstant(minWorkDate);
        while (size > 0 && availableDates.size() < size && count < maxCount) {
            if (theDateCanDepart(carNo, minWorkDate, isInternal, stationCode)) {
                availableDates.add(minWorkDate);
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(minWorkDate);
            calendar.add(Calendar.DATE, 1);
            minWorkDate = calendar.getTime();
            count++;
        }
        return availableDates;
    }

    /**
     * 根據請求來源和車況取得最快備車日期
     *
     * @param fromInternal 是否為來自收銀台的請求
     * @param carNo        車輛編號
     */
    protected Date getMinWorkDate(boolean fromInternal, String carNo) {
        Calendar calendar = Calendar.getInstance();
        return getMinWorkDate(fromInternal, carNo, calendar);
    }

    protected Date getMinWorkDate(boolean fromInternal, String carNo, Calendar calendar) {
        Cars car = carsService.findByCarNo(carNo);
        int workDays = Optional.ofNullable(car.getPrepWorkdays()).orElse(car.getCarState().getWorkDays());
        return getMinWorkDateInternal(fromInternal, calendar, workDays);
    }

    /**
     * 依據 Cars 物件取得最快備車日
     */
    protected Date getMinWorkDate(boolean fromInternal, @NonNull Cars car, Calendar calendar) {
        int workDays = Optional.ofNullable(car.getPrepWorkdays()).orElse(car.getCarState().getWorkDays());
        return getMinWorkDateInternal(fromInternal, calendar, workDays);
    }

    /**
     * 共用邏輯：依據 fromInternal、calendar、workDays 計算備車日
     */
    private Date getMinWorkDateInternal(boolean fromInternal, Calendar calendar, int workDays) {
        if (fromInternal) {
            calendar.add(Calendar.DATE, 1);
            return calendar.getTime();
        }
        List<String> normalHolidays = srentalServer.getNormalHolidayCalendar();
        if (normalHolidays.isEmpty()) {
            if (workDays > 0) {
                calendar.add(Calendar.DATE, workDays);
            }
        } else {
            while (workDays >= 0) {
                calendar.add(Calendar.DATE, 1);
                String formattedDate = DateUtils.toDateString(calendar.getTime(), "yyyyMMdd");
                if (!normalHolidays.contains(formattedDate)) {
                    workDays--;
                }
            }
        }
        return calendar.getTime();
    }

    /**
     * 拿取主約正在進行中訂單
     */
    public Orders getDepartOrdersByMainContract(String mainContractNo) {
        MainContract mainContract = Optional.ofNullable(mainContractRepository.getMainContractByNo(mainContractNo, true))
            .orElseThrow(() -> new SubscribeException(MAIN_CONTRACT_NOT_FOUND));
        return mainContract.getContracts().stream().map(contract ->
            contract.getOrders().stream().filter(order -> order.getStatus() == OrderStatus.DEPART.getStatus()).findAny().orElse(null)
        ).filter(Objects::nonNull).findAny().orElse(null);
    }
}
