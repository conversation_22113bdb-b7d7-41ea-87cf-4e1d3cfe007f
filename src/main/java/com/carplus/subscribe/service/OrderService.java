package com.carplus.subscribe.service;

import carplus.common.enums.etag.ETagPayFlow;
import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusCode;
import carplus.common.response.exception.AuthException;
import carplus.common.response.exception.BadRequestException;
import carplus.common.response.exception.LogicException;
import carplus.common.utils.BeanUtils;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.*;
import com.carplus.subscribe.db.mysql.dto.ETagInfoDTO;
import com.carplus.subscribe.db.mysql.dto.OrderPriceInfoDTO;
import com.carplus.subscribe.db.mysql.dto.OrdersDTO;
import com.carplus.subscribe.db.mysql.entity.BuChangeLog;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.CarRegistration;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.*;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.db.mysql.entity.econtract.EContract;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.db.mysql.entity.payment.Account;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.event.*;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.mapper.contractinfo.ContractAddReqMapper;
import com.carplus.subscribe.model.*;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserResponse;
import com.carplus.subscribe.model.authority.AdminUser;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.config.AdminRoleConfig;
import com.carplus.subscribe.model.config.YesChargingPoint;
import com.carplus.subscribe.model.credit.Auditor;
import com.carplus.subscribe.model.credit.AutoCreditInfo;
import com.carplus.subscribe.model.credit.CreditInfo;
import com.carplus.subscribe.model.crs.CarBaseInfoQueryResponse;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.crs.PurchaseProjectCarSearchResponse;
import com.carplus.subscribe.model.invoice.Invoice;
import com.carplus.subscribe.model.invoice.InvoiceInfo;
import com.carplus.subscribe.model.invoice.InvoiceNewRequest;
import com.carplus.subscribe.model.invoice.InvoiceRequest;
import com.carplus.subscribe.model.lrental.ContractAddReq;
import com.carplus.subscribe.model.lrental.ContractCustomerCreateRequest;
import com.carplus.subscribe.model.lrental.ContractSearchRep;
import com.carplus.subscribe.model.order.*;
import com.carplus.subscribe.model.parking.OrderDepartRecordRequest;
import com.carplus.subscribe.model.parking.OrderDepartRecordResponse;
import com.carplus.subscribe.model.payment.req.AccountRecord;
import com.carplus.subscribe.model.payment.req.AccountSettlementRequest;
import com.carplus.subscribe.model.payment.req.PaymentRequest;
import com.carplus.subscribe.model.priceinfo.*;
import com.carplus.subscribe.model.priceinfo.req.InternalAccidentRequest;
import com.carplus.subscribe.model.priceinfo.req.OrderPriceInfoRefundRetryRequest;
import com.carplus.subscribe.model.region.Area;
import com.carplus.subscribe.model.region.City;
import com.carplus.subscribe.model.request.contract.*;
import com.carplus.subscribe.model.request.contract.CloseRequest;
import com.carplus.subscribe.model.request.dealer.DealerOrderCloseRequest;
import com.carplus.subscribe.model.request.dealer.DealerOrderDepartRequest;
import com.carplus.subscribe.model.request.dealer.DealerOrderUpdateRequest;
import com.carplus.subscribe.model.request.depart.CarDepartFlowRequest;
import com.carplus.subscribe.model.request.depart.CarDepartRequest;
import com.carplus.subscribe.model.request.dropoff.*;
import com.carplus.subscribe.model.request.order.*;
import com.carplus.subscribe.model.request.priceinfo.ExtraFeeRequest;
import com.carplus.subscribe.model.request.priceinfo.MileageDiscountRequest;
import com.carplus.subscribe.model.request.task.DepartTaskRequest;
import com.carplus.subscribe.model.response.order.OrderCSV;
import com.carplus.subscribe.model.response.order.OrderDTO;
import com.carplus.subscribe.model.response.order.OrderQueryResponse;
import com.carplus.subscribe.model.response.order.UserOrderResponse;
import com.carplus.subscribe.model.response.task.TaskDetailResponse;
import com.carplus.subscribe.model.subscribelevel.SubscribeLevelResponse;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.server.GoSmartServer;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.server.cars.LrentalServer;
import com.carplus.subscribe.utils.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.SingletonMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.persistence.LockModeType;
import javax.persistence.criteria.Predicate;
import java.nio.charset.Charset;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.constant.CarPlusConstant.CARPLUS_COMPANY_CODE;
import static com.carplus.subscribe.enums.LegalOperationReason.*;
import static com.carplus.subscribe.enums.OrderStatus.*;
import static com.carplus.subscribe.enums.PayFor.Depart;
import static com.carplus.subscribe.enums.PayFor.SecurityDeposit;
import static com.carplus.subscribe.enums.PaymentCategory.PayAuth;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.*;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.*;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static com.carplus.subscribe.mapper.priceinfo.PriceInfoMapper.toExtraFeeReq;
import static com.carplus.subscribe.utils.CarsUtil.*;
import static com.carplus.subscribe.utils.DateUtil.SLASH_FORMATTER_WITHOUT_TIME;
import static com.carplus.subscribe.utils.DateUtil.convertToStartOfInstant;
import static java.time.temporal.ChronoField.HOUR_OF_DAY;
import static java.time.temporal.ChronoField.MINUTE_OF_HOUR;
import static java.time.temporal.ChronoUnit.DAYS;

@Slf4j
@Service
public class OrderService {
    public static final String ORDER_STATE = "order:state:";
    @Autowired
    private EtagInfoRepository etagInfoRepository;
    @Autowired
    private MainContractRepository mainContractRepository;
    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private SubscribeLevelService subscribeLevelService;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private OrderPriceInfoRepository orderPriceInfoRepository;
    @Autowired
    private AuthServer authServer;
    @Autowired
    private AuthorityServer authorityServer;
    @Autowired
    private ContractService contractService;
    @Autowired
    private EContractService econtractService;
    @Autowired
    private CsatService csatService;
    @Autowired
    private CrsService crsService;
    @Autowired
    @Lazy
    private PriceInfoService priceInfoService;
    @Autowired
    private CarsService carsService;
    @Autowired
    private StationService stationService;
    @Autowired
    private ETagService eTagService;
    @Lazy
    @Autowired
    private PaymentServiceV2 paymentService;
    @Autowired
    private CheckoutService checkoutService;
    @Lazy
    @Autowired
    private OrderService self;
    @Autowired
    private InvoiceServiceV2 invoiceServiceV2;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private NotifyToCService notifyToCService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private SensorWebhookService sensorWebhookService;
    @Autowired
    private BuChangeService buChangeService;
    @Autowired
    private DealerOrderService dealerOrderService;
    @Autowired
    private ObjectMapper objectMapper;
    @Value("${station.subscribe}")
    private String subscribeStationCode;
    @Autowired
    private GoSmartServer goSmartServer;
    @Autowired
    private LrentalServer lrentalServer;
    @Autowired
    private MattermostServer mattermostServer;
    @Autowired
    private RentalTaskService rentalTaskService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private SkuService skuService;
    @Autowired
    private CarRegistrationService carRegistrationService;
    @Autowired
    private ContractLogic contractLogic;
    @Autowired
    private LrentalContractService lrentalContractService;
    @Autowired
    private InsuranceService insuranceService;
    @Autowired
    private ContractAddReqMapper contractAddReqMapper;
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders createOrderFromContract(Contract contract, @Nullable Invoice invoice, @Nullable String orderRemark, int month,
                                          boolean isTemp, @Nullable Integer acctId, @Nullable MemberInfo memberInfo,
                                          YesChargingPoint point, List<MerchandiseReq> merchList, @Nullable String custRemark, OrderPlatform orderPlatform) {
        Orders order = new Orders();
        order.setContract(contract);

        if (memberInfo != null && StringUtils.isNotBlank(memberInfo.getMemberId())) {
            order.setMemberId(memberInfo.getMemberId());
        } else {
            order.setAcctId(acctId);
        }
        order.setContractNo(contract.getContractNo());
        MainContract mainContract = contract.getMainContract();
        if (contract.getOrders() == null || contract.getOrders().isEmpty()) {
            order.setStage(1);
            order.setExpectStartDate(contract.getExpectStartDate());
            if (mainContract.getStartDate() == null) {
                order.setIsNewOrder(true);
            }
        } else {
            Orders preOrder = contract.getOrders().stream().max(Comparator.comparing(Orders::getStage)).get();
            order.setStage(preOrder.getStage() < 1 ? 1 : (preOrder.getStage() + 1));
            order.setExpectStartDate(preOrder.getExpectEndDate().plus(1, ChronoUnit.DAYS));
        }
        if (mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDepositDate() != null) {
            order.setSecurityDepositDate(Instant.now());
        }
        // 初始化 remarks 集合
        List<Remark> remarks = new ArrayList<>();
        // 儲存 request 訂單註記 (com.carplus.subscribe.model.request.contract.ContractCreateReq.remark) 至訂單備註
        if (StringUtils.isNotBlank(orderRemark)) {
            remarks.add(buildRemark(orderRemark, memberInfo));
        }
        // 主約客戶備註改儲存至訂單備註
        if (order.getIsNewOrder() && StringUtils.isNotBlank(custRemark)) {
            remarks.add(buildCustRemark(custRemark));
        }
        order.setRemarks(remarks);
        order.setOrderPlatform(orderPlatform);

        order.setStatus(CREDIT_PENDING.getStatus());
        order.setInvoice(Optional.ofNullable(invoice).orElseGet(Invoice::defaultInvoice));

        order.setMonth(month);
        order.setExpectEndDate(DateUtil.calculateNewEndDate(order.getExpectStartDate(), month).toInstant());


        if (!isTemp) {
            order.setOrderNo(generateOrderNo());
            priceInfoService.createOrderPriceInfos(order, point, merchList);
            orderRepository.saveAndFlush(order);
        }
        return order;
    }

    /**
     * 產生訂單號碼
     */
    @Lock(LockModeType.READ)
    public String generateOrderNo() {
        List<String> orderNos = orderRepository.getToDayOrderNos();
        String date = DateUtils.toDateString(new Date(), "yyyyMMdd", DateUtils.ZONE_TPE);
        Random r = new Random();
        int randomNo = (int) (r.nextDouble() * 9999);
        String orderNo = "M" + date + OrderUtils.addChars(randomNo + "", "0", 4);
        while (orderNos.contains(orderNo)) {
            randomNo = (int) (r.nextDouble() * 9999);
            orderNo = "M" + date + OrderUtils.addChars(randomNo + "", "0", 4);
        }
        return orderNo;
    }

    /**
     * 更新訂單
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders updateOrder(Orders order) {
        return orderRepository.save(order);
    }

    /**
     * 收銀台訂單更新
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders updateOrder(String orderNo, OrderUpdateRequest orderUpdateRequest, AdminUser admin, String memberId) {
        Orders orders = getOrder(orderNo);
        updateOrderValidate(admin, orders, orderUpdateRequest);
        Contract contract = orders.getContract();
        MainContract mainContract = contract.getMainContract();
        Cars oriCar = carsService.findByPlateNo(orderUpdateRequest.getOriPlateNo());
        Cars useCar = carsService.findByPlateNo(orderUpdateRequest.getPlateNo());
        CarBaseInfoSearchResponse useCarCrs = crsService.getCar(orderUpdateRequest.getPlateNo());
        if (orders.getIsNewOrder()) {
            if (contract.getStartDate() == null) {
                contract.setExpectStartDate(orders.getExpectStartDate());
                contract.setExpectEndDate(contract.getExpectStartDate().atZone(DateUtils.ZONE_TPE).plusYears(1).minusDays(1).toInstant());
            }
            if (mainContract.getStartDate() == null) {
                mainContract.setExpectStartDate(orders.getExpectStartDate());
                mainContract.setExpectEndDate(contract.getExpectEndDate());
            }
            mainContract.getOriginalPriceInfo().setMonth(orderUpdateRequest.getMonth());
        }

        if (orders.getStatus() < DEPART.getStatus()) {
            // 異動期數費用
            if (orderUpdateRequest.getMonth() > 0 && orderUpdateRequest.getMonth() != orders.getMonth()) {
                int oldMonth = orders.getMonth();
                int newMonth = orderUpdateRequest.getMonth();
                orders.setMonth(newMonth);
                int oldMaxStage = calculateMaxStage(oldMonth);
                int newMaxStage = calculateMaxStage(newMonth);
                List<OrderPriceInfo> newOrderPriceInfoList = priceInfoService.calculateOrderPrice(orders);
                List<OrderPriceInfo> oldOrderPriceInfoList = priceInfoService.getPriceInfosByOrder(orderNo);
                List<OrderPriceInfo> padiOrderPriceInfoList = oldOrderPriceInfoList.stream().filter(PriceInfoInterface::isPaid).collect(Collectors.toList());
                List<OrderPriceInfo> orderPriceInfoListToSave;

                // 共通邏輯：移除舊的月費和保險費
                List<OrderPriceInfo> orderPriceInfoListToRemove = new ArrayList<>(getOrderPriceInfoListToRemove(oldOrderPriceInfoList, Math.min(oldMaxStage, newMaxStage), oldMonth == newMonth));

                if (oldMaxStage > newMaxStage) {
                    // 刪除超出新期數的 orderPriceInfo
                    orderPriceInfoListToRemove.addAll(priceInfoService.getPriceInfosByOrder(orderNo).stream()
                        .filter(orderPriceInfo -> orderPriceInfo.getStage() > newMaxStage)
                        .collect(Collectors.toList()));

                    orderPriceInfoListToSave = newOrderPriceInfoList.stream()
                        .filter(newOrderPriceInfo -> newOrderPriceInfo.getCategory() == MileageFee || (newOrderPriceInfo.getStage() == newMaxStage
                            && isMonthlyFeeOrInsuranceUnpaid(newOrderPriceInfo, orderPriceInfoListToRemove, padiOrderPriceInfoList)))
                        .collect(Collectors.toList());
                } else {
                    orderPriceInfoListToSave = newOrderPriceInfoList.stream()
                        .filter(
                            newOrderPriceInfo -> newOrderPriceInfo.getCategory() == MileageFee || (newOrderPriceInfo.getStage() == oldMaxStage && isMonthlyFeeOrInsuranceUnpaid(newOrderPriceInfo, orderPriceInfoListToRemove, padiOrderPriceInfoList))
                                || newOrderPriceInfo.getStage() > oldMaxStage)
                        .collect(Collectors.toList());
                    // 若月數沒異動，不異動里程費
                    if (oldMonth == newMonth) {
                        orderPriceInfoListToSave = orderPriceInfoListToSave.stream().filter(opiToSave -> opiToSave.getCategory() != MileageFee).collect(Collectors.toList());
                    }
                }
                // detail中的month與day重新異動
                resetMonthlyFeeMonthAndDays(newOrderPriceInfoList, orderPriceInfoListToSave);
                orderPriceInfoRepository.deleteAll(orderPriceInfoListToRemove);
                orderPriceInfoRepository.saveAll(orderPriceInfoListToSave);
            }
            // 異動是否額外保障
            processingInsuranceChange(orderNo, orderUpdateRequest, memberId, orders, contract);
            // 異動是否代步車
            processingReplacementChange(orderNo, orderUpdateRequest, memberId, orders, contract);
            // 出車前才可異動車牌號碼
            if (StringUtils.isNotBlank(orderUpdateRequest.getPlateNo()) && !orderUpdateRequest.getPlateNo().equals(mainContract.getPlateNo())) {


                // 檢查車籍
                if (useCar.getLaunched() == CarDefine.Launched.deprecate) {
                    throw new SubscribeException(CAR_LAUNCHED_DEPRECATE);
                }
                if (useCar.getLaunched() == CarDefine.Launched.accident) {
                    throw new SubscribeException(CAR_LAUNCHED_ACCIDENT);
                }
                orders.setDepartMileage(null);
                if (orders.getStatus() > CREDITED.getStatus()) {
                    // 新車從空車00狀態變更為鎖車20狀態
                    changePlateNo(orders, mainContract, useCar, oriCar, memberId);
                    // 訂單換車號 且 車輛庫位 = 長租
                    if (CarsUtil.isCarPlusCar(useCar.getVatNo()) && Objects.equals(BuIdEnum.lRental.getCode(), useCarCrs.getBuId())) {
                        notifyService.notifyOrderReceivedConfirm(orders, useCar, useCarCrs);
                    }
                }
            }
        }
        if ((!Objects.equals(orderUpdateRequest.getReturnStationCode(), orderUpdateRequest.getOriReturnStationCode())
            || !Objects.equals(orderUpdateRequest.getDepartStationCode(), orderUpdateRequest.getOriDepartStationCode())
            && orders.getStatus() >= BOOKING.getStatus())) {
            carsService.updateLocationStationBasedOnSgType(mainContract, useCar);
        }
        econtractService.updateEContractBeforeSign(orders, useCar.getCarNo());
        contractService.updateContract(contract);
        contractService.updateMainContract(mainContract);
        orderRepository.save(orders);
        if (!DateUtils.isSameDay(Date.from(orderUpdateRequest.getOriExpectDepartDate()), Date.from(orderUpdateRequest.getExpectDepartDate()))) {
            checkoutService.modifyMonthlyItem(orders);
        }
        if (orderUpdateRequest.isOrderChanged() && orders.getStatus() >= BOOKING.getStatus()) {
            AuthUser user = authServer.getUserWithRetry(mainContract.getAcctId());
            notifyService.notifyOrderUpdate(orders, user, orderUpdateRequest);
            notifyToCService.notifyModifyOrder(orders, user, mainContract.getPlateNo());
        }

        return orders;
    }

    /**
     * 異動額外保險
     */
    private void processingInsuranceChange(String orderNo, OrderUpdateRequest orderUpdateRequest, String memberId, Orders orders, Contract contract) {
        if (orderUpdateRequest.getDisclaimer() != null && !Objects.equals(orderUpdateRequest.getDisclaimer(), contract.getDisclaimer())) {
            // 取消額外保障
            PriceInfoWrapper insuranceWrapper = priceInfoService.getPriceInfoWrapper(orderNo).getByCategory(Insurance);
            if (Boolean.TRUE.equals(contract.getDisclaimer())) {
                List<OrderPriceInfo> insuranceList = insuranceWrapper.getByType(Pay).getList();
                if (!insuranceList.isEmpty()) {
                    OrderPriceInfo insurance = insuranceList.get(0);
                    // 如已付款
                    if (insurance.getReceivedAmount() > 0) {
                        CarDropOffDiscountRequest dropOffRequest = new CarDropOffDiscountRequest();
                        dropOffRequest.setPriceInfoPayId(insurance.getId());
                        dropOffRequest.setCategory(Insurance);
                        dropOffRequest.setAgree(true);
                        dropOffRequest.setOriginAmount(insurance.getAmount());
                        dropOffRequest.setDiscount(insurance.getAmount());
                        dropOffRequest.setOriginalDiscount(0);
                        dropOffCarDiscount(orderNo, dropOffRequest, memberId);

                    }
                }
                orderPriceInfoRepository.deleteAll(insuranceList.stream().filter(orderPriceInfo -> orderPriceInfo.getReceivedAmount() == 0).collect(Collectors.toList()));
                // 增加額外保障
            } else if (Boolean.FALSE.equals(contract.getDisclaimer())) {
                contract.setDisclaimer(true);
                List<OrderPriceInfo> insuranceList = insuranceWrapper.getList();
                orderPriceInfoRepository.deleteAll(insuranceList.stream().filter(orderPriceInfo -> orderPriceInfo.getReceivedAmount() == 0).collect(Collectors.toList()));
                List<Integer> paidStages =
                    insuranceList.stream().filter(orderPriceInfo -> orderPriceInfo.getReceivedAmount() > 0 && Pay.getCode() == orderPriceInfo.getType()).map(OrderPriceInfo::getStage).collect(Collectors.toList());
                List<Integer> navigateStages =
                    insuranceList.stream().filter(orderPriceInfo -> orderPriceInfo.getReceivedAmount() > 0 && Pay.getCode() != orderPriceInfo.getType()).map(OrderPriceInfo::getStage).collect(Collectors.toList());
                // 已經付過款項的，或是有退費過的才可以加入
                orderPriceInfoRepository.saveAll(priceInfoService.calculateOrderPrice(orders).stream().filter(orderPriceInfo -> orderPriceInfo.getCategory() == Insurance)
                    .filter(opi -> !paidStages.contains(opi.getStage()) || navigateStages.contains(opi.getStage())).collect(Collectors.toList()));
            }
            contract.setDisclaimer(orderUpdateRequest.getDisclaimer());
        }
    }

    /**
     * 異動代步車
     */
    private void processingReplacementChange(String orderNo, OrderUpdateRequest orderUpdateRequest, String memberId, Orders orders, Contract contract) {
        if (orderUpdateRequest.getReplacement() != null && !Objects.equals(orderUpdateRequest.getReplacement(), contract.getReplacement())) {
            // 取消額外保障
            if (Boolean.TRUE.equals(contract.getReplacement())) {
                List<OrderPriceInfo> replacementList = priceInfoService.getPriceInfosByOrder(orderNo, Replacement, Pay);
                if (!replacementList.isEmpty()) {
                    OrderPriceInfo replacement = replacementList.get(0);
                    // 如已付款
                    if (replacement.getReceivedAmount() > 0) {
                        CarDropOffDiscountRequest dropOffRequest = new CarDropOffDiscountRequest();
                        dropOffRequest.setPriceInfoPayId(replacement.getId());
                        dropOffRequest.setCategory(Replacement);
                        dropOffRequest.setAgree(true);
                        dropOffRequest.setOriginAmount(replacement.getAmount());
                        dropOffRequest.setDiscount(replacement.getAmount());
                        dropOffRequest.setOriginalDiscount(0);
                        dropOffCarDiscount(orderNo, dropOffRequest, memberId);

                    }
                }
                orderPriceInfoRepository.deleteAll(replacementList.stream().filter(orderPriceInfo -> orderPriceInfo.getReceivedAmount() == 0).collect(Collectors.toList()));
                // 增加額外保障
            } else if (Boolean.FALSE.equals(contract.getReplacement())) {
                contract.setReplacement(true);
                PriceInfoWrapper replacementWrapper = priceInfoService.getPriceInfoWrapper(orderNo).getByCategory(Replacement);
                orderPriceInfoRepository.deleteAll(replacementWrapper.getList().stream().filter(orderPriceInfo -> orderPriceInfo.getReceivedAmount() == 0).collect(Collectors.toList()));
                List<Integer> paidStages =
                    replacementWrapper.getByType(Pay).getPaid().getList().stream().map(OrderPriceInfo::getStage).collect(Collectors.toList());
                List<Integer> navigateStages =
                    replacementWrapper.excludeType(Pay).getPaid().getList().stream().map(OrderPriceInfo::getStage).collect(Collectors.toList());
                // 已經付過款項的，或是有退費過的才可以加入
                orderPriceInfoRepository.saveAll(priceInfoService.calculateOrderPrice(orders).stream().filter(orderPriceInfo -> orderPriceInfo.getCategory() == Replacement)
                    .filter(opi -> !paidStages.contains(opi.getStage()) || navigateStages.contains(opi.getStage())).collect(Collectors.toList()));
            }
            contract.setReplacement(orderUpdateRequest.getReplacement());
        }
    }

    private List<OrderPriceInfo> getOrderPriceInfoListToRemove(List<OrderPriceInfo> oldOrderPriceInfoList, int stage, boolean isSameMonth) {
        List<OrderPriceInfo> list = oldOrderPriceInfoList.stream()
            .filter(orderPriceInfo -> (orderPriceInfo.getStage() == stage && isMonthlyFeeOrInsuranceMileageFeeUnpaid(orderPriceInfo))
                || (orderPriceInfo.getCategory() == MileageFee && isMonthlyFeeOrInsuranceMileageFeeUnpaid(orderPriceInfo))).collect(Collectors.toList());
        //若月份沒異動，則不將里程數加入移除清單
        if (isSameMonth) {
            list = list.stream().filter(orderPriceInfo -> (orderPriceInfo.getCategory() != MileageFee && isMonthlyFeeOrInsuranceMileageFeeUnpaid(orderPriceInfo))).collect(Collectors.toList());
        }
        return list;
    }

    private void resetMonthlyFeeMonthAndDays(List<OrderPriceInfo> newMonthlyOrderPrice, List<OrderPriceInfo> oriOrderPriceInfoList) {
        Map<Integer, OrderPriceInfo> newMonthlyOrderPriceMap = newMonthlyOrderPrice.stream()
            .filter(newOpi -> newOpi.getCategory() == MonthlyFee && newOpi.getType() == Pay.getCode())
            .collect(Collectors.toMap(OrderPriceInfo::getStage, Function.identity()));
        oriOrderPriceInfoList.forEach(opi -> {
            OrderPriceInfo newOpi = newMonthlyOrderPriceMap.get(opi.getStage());
            if (newOpi != null) {
                PriceInfoDetail detail = Optional.ofNullable(opi.getInfoDetail()).orElse(new PriceInfoDetail());
                detail.setDay(Optional.ofNullable(newOpi.getInfoDetail()).map(PriceInfoDetail::getDay).orElse(null));
                detail.setMonth(Optional.ofNullable(newOpi.getInfoDetail()).map(PriceInfoDetail::getMonth).orElse(null));
            }
        });

    }

    private int calculateMaxStage(int oldMonth) {
        return (int) Math.ceil((double) oldMonth / 3);
    }

    private boolean isMonthlyFeeOrInsuranceMileageFeeUnpaid(OrderPriceInfo orderPriceInfo) {
        return (orderPriceInfo.getCategory() == MonthlyFee || orderPriceInfo.getCategory() == Insurance || orderPriceInfo.getCategory() == MileageFee)
            && !orderPriceInfo.isPaid();
    }

    private boolean isMonthlyFeeOrInsuranceUnpaid(OrderPriceInfo newOrderPriceInfo, List<OrderPriceInfo> orderPriceInfoListToRemove, List<OrderPriceInfo> paidOrderPriceInfoList) {
        return (newOrderPriceInfo.getCategory() == MonthlyFee && orderPriceInfoListToRemove.stream().anyMatch(oldOrderPriceInfo -> oldOrderPriceInfo.getCategory() == MonthlyFee)
            && paidOrderPriceInfoList.stream().noneMatch(oldOrderPriceInfo -> oldOrderPriceInfo.getCategory() == MonthlyFee && Objects.equals(newOrderPriceInfo.getStage(), oldOrderPriceInfo.getStage())))
            || (newOrderPriceInfo.getCategory() == Insurance && orderPriceInfoListToRemove.stream().anyMatch(oldOrderPriceInfo -> oldOrderPriceInfo.getCategory() == Insurance)
            && paidOrderPriceInfoList.stream().noneMatch(oldOrderPriceInfo -> oldOrderPriceInfo.getCategory() == Insurance && Objects.equals(newOrderPriceInfo.getStage(), oldOrderPriceInfo.getStage())));
    }

    /**
     * 訂單異動檢查
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public boolean updateOrderValidate(AdminUser admin, Orders order, OrderUpdateRequest orderUpdateRequest) {
        // 確認狀態
        if (order.getStatus() >= CLOSE_WITH_SUB.getStatus()) {
            throw new BadRequestException("結案前才可編輯訂單資料");
        }
        Long departDiffDay = null;
        Contract contract = order.getContract();
        MainContract mainContract = contract.getMainContract();
        boolean updateExpectReturnDate = false;
        Stations returnStation;

        if ((orderUpdateRequest.getMonth() > 0 && !Objects.equals(orderUpdateRequest.getMonth(), order.getMonth()))
            && !order.getIsNewOrder() && mainContract.getStatus() != ContractStatus.CREATE.getCode()) {
            throw new BadRequestException("必須為新單，且尚未出車才可異動訂閱月份");
        }

        if (orderUpdateRequest.getDisclaimer() != null && !Objects.equals(orderUpdateRequest.getDisclaimer(), contract.getDisclaimer())
            && order.getStatus() <= CREDITED.getStatus()) {
            throw new BadRequestException("必須為尚未出車才可異動額外保障");
        }

        // 設定原始資料
        orderUpdateRequest.setOriDepartStationCode(mainContract.getDepartStationCode());
        orderUpdateRequest.setOriReturnStationCode(mainContract.getReturnStationCode());
        orderUpdateRequest.setOriExpectDepartDate(Instant.ofEpochMilli(order.getExpectStartDate().toEpochMilli()));
        orderUpdateRequest.setOriExpectReturnDate(Instant.ofEpochMilli(order.getExpectEndDate().toEpochMilli()));
        orderUpdateRequest.setOriPlateNo(mainContract.getPlateNo());


        if (orderUpdateRequest.getInvoice() != null) {
            order.setInvoice(orderUpdateRequest.getInvoice());
        }
        // 異動預計出車站所
        if (!Objects.equals(orderUpdateRequest.getDepartStationCode(), mainContract.getDepartStationCode())) {
            if (order.getStatus() >= DEPART.getStatus()) {
                throw new BadRequestException("訂單已出車，不可異動預計出車站所");
            }
            orderUpdateRequest.setOrderChanged(true);
            mainContract.setDepartStationCode(orderUpdateRequest.getDepartStationCode());

            // 檢查是否有調度費
            PriceInfo originalPriceInfo = order.getContract().getMainContract().getOriginalPriceInfo();
            OrderPriceInfo newDisPatchFee = priceInfoService.calculateDispatch(originalPriceInfo, order.getPlateNo(),
                order.getContract().getMainContract().getDepartStationCode(), order.getContract().getMainContract().getExpectStartDate());
            OrderPriceInfo orderDispatchPriceInfo = priceInfoService.getPriceInfosByOrder(order.getOrderNo()).stream().filter(priceInfo ->
                priceInfo.getType() == Pay.getCode() && priceInfo.getCategory() == Dispatch).findAny().orElse(null);
            if (orderDispatchPriceInfo != null) {
                if (orderDispatchPriceInfo.getReceivedAmount() == 0) {
                    // 異動調度費
                    orderDispatchPriceInfo.setAmount(newDisPatchFee.getAmount());
                    orderPriceInfoRepository.save(orderDispatchPriceInfo);
                } else {
                    log.info("{}調度費已付款，不可異動", order.getOrderNo());
                }
            } else if (newDisPatchFee.getAmount() > 0) {
                // 新增調度費
                newDisPatchFee.setOrderNo(order.getOrderNo());
                orderPriceInfoRepository.save(newDisPatchFee);
            }
        }
        // 異動預計還車站所
        if (!Objects.equals(orderUpdateRequest.getReturnStationCode(), mainContract.getReturnStationCode())) {
            if (order.getStatus() >= ARRIVE_NO_CLOSE.getStatus()) {
                throw new BadRequestException("訂單已還車，不可異動預計還車站所");
            }
            updateExpectReturnDate = true;
            orderUpdateRequest.setOrderChanged(true);
            mainContract.setReturnStationCode(orderUpdateRequest.getReturnStationCode());

        }
        // 異動訂單出車時間
        if (!Objects.equals(orderUpdateRequest.getExpectDepartDate(), order.getExpectStartDate()) || !Objects.equals(orderUpdateRequest.getMonth(), order.getMonth())) {
            if (order.getStatus() >= DEPART.getStatus()) {
                throw new BadRequestException("訂單已出車，不可異動預計出車時間");
            }
            if (!order.getIsNewOrder()) {
                throw new BadRequestException("不為第一期訂單不可異動預計出車時間");
            }
            if (order.getStartDate() != null) {
                throw new BadRequestException("已有實際出車時間，不可異動預計出車時間");
            }
            departDiffDay = DateUtil.calculateDiffDate(order.getExpectStartDate(), orderUpdateRequest.getExpectDepartDate(), DAYS);
            updateExpectReturnDate = true;
            orderUpdateRequest.setOrderChanged(true);
            order.setExpectStartDate(orderUpdateRequest.getExpectDepartDate());
        }

        if (updateExpectReturnDate) {
            returnStation = stationService.findByStationCode(orderUpdateRequest.getReturnStationCode());
            int hours = 23;
            int minutes = 59;
            int seconds = 59;
            try {
                hours = Integer.parseInt(returnStation.getEndHours().substring(0, 2));
                minutes = Integer.parseInt(returnStation.getEndHours().substring(2, 4));
                seconds = 0;
            } catch (Exception ignore) {
                //ignore;
            }
            if (departDiffDay != null || !Objects.equals(orderUpdateRequest.getMonth(), order.getMonth())) {
                if (order.getIsNewOrder()) {
                    // 原始預計迄租日
                    Instant originalEndDate = order.getExpectEndDate();
                    // 直接用新預計起租日 + N月 - 1天計算新預計迄租日
                    Instant newEndDate = DateUtil.calculateNewEndDate(order.getExpectStartDate(), orderUpdateRequest.getMonth())
                        .withHour(hours).withMinute(minutes).withSecond(seconds).toInstant();
                    order.setExpectEndDate(newEndDate);
                    // 計算原始預計迄租日和新預計迄租日的差異天數
                    departDiffDay = DateUtil.calculateDiffDate(convertToStartOfInstant(originalEndDate), convertToStartOfInstant(newEndDate), DAYS);
                    priceInfoService.updateOrderPriceLastPayDay(order, departDiffDay.intValue());
                } else {
                    throw new SubscribeException(MODIFY_ORDER_DEPART_DATE_INVALIDATE);
                }

            }
            if (!Objects.equals(orderUpdateRequest.getMonth(), order.getMonth())) {
                if (isEContractSigned(contract.getContractNo())) {
                    throw new SubscribeException(CANNOT_MODIFY_ORDER_MONTH_AFTER_ECONTRACT_SIGNED);
                }
            }
        }
        mainContract.setReferInfo(orderUpdateRequest.getReferInfo());
        if (StringUtils.isNotBlank(orderUpdateRequest.getDepartMemberId())
            && !Objects.equals(order.getDepartMemberId(), orderUpdateRequest.getDepartMemberId())) {
            if (admin.getRoles().stream().noneMatch(AdminRoleConfig::isSuperAdmin)) {
                throw new BadRequestException("非管理者不可異動");
            }
            if (order.getStatus() >= DEPART.getStatus()) {
                order.setDepartMemberId(orderUpdateRequest.getDepartMemberId());
            } else {
                throw new BadRequestException(String.format("訂單狀態需為[%s]狀態後(含)才可異動出車人員編號", DEPART.getName()));
            }
        }

        if (StringUtils.isNotBlank(orderUpdateRequest.getReturnMemberId())
            && !Objects.equals(order.getReturnMemberId(), orderUpdateRequest.getReturnMemberId())) {
            if (admin.getRoles().stream().noneMatch(AdminRoleConfig::isSuperAdmin)) {
                throw new BadRequestException("非管理者不可異動");
            }
            if (order.getStatus() >= ARRIVE_NO_CLOSE.getStatus()) {
                order.setReturnMemberId(orderUpdateRequest.getReturnMemberId());
            } else {
                throw new BadRequestException(String.format("訂單狀態需為[%s]狀態後(含)才可異動還車人員編號", ARRIVE_NO_CLOSE.getName()));
            }
        }
        // 檢查更換車籍是否同方案
        validateSubscribeLevelWithMainContract(orderUpdateRequest.getPlateNo(), mainContract);
        return updateExpectReturnDate;
    }

    private boolean isEContractSigned(String contractNo) {
        List<EContract> ecs = econtractService.getEContracts(contractNo, Lists.newArrayList(EContractType.E_CONTRACT.name()));
        if (ecs.isEmpty()) {
            return false;
        }
        return ecs.get(0) != null && ecs.get(0).getEContractSignDate() != null;
    }

    public void validateSubscribeLevelWithMainContract(String plateNo, MainContract mainContract) {
        if (StringUtils.isBlank(plateNo) || plateNo.equals(mainContract.getPlateNo())) {
            return;
        }

        Cars cars = carsService.findByPlateNo(plateNo);

        // 比較是否啟用優惠月費
        if (cars.isMonthlyDiscounted() != mainContract.getOriginalPriceInfo().isMonthlyDiscounted()) {
            throw new BadRequestException(CAR_IS_MONTHLY_DISCOUNTED_NOT_MATCH_WITH_MAIN_CONTRACT.getMsg());
        }

        SubscribeLevel carSubscribeLevel = subscribeLevelService.findByLevel(cars.getSubscribeLevel());

        boolean isDiscountLevelEnabled = subscribeLevelService.isDiscountLevelEnabled(cars, carSubscribeLevel);

        if (!isSubscribeLevelMatch(cars, mainContract)) {
            if (!isDiscountLevelEnabled || !isDiscountLevelMatch(mainContract, carSubscribeLevel)) {
                if (!isDiscountLevelEnabled && isDiscountLevelMatch(mainContract, carSubscribeLevel)) {
                    throw new BadRequestException(CAR_DISCOUNT_LEVEL_NOT_MATCH_WITH_MAIN_CONTRACT.getMsg());
                } else {
                    throw new BadRequestException(CAR_SUBSCRIBE_LEVEL_NOT_MATCH_WITH_MAIN_CONTRACT.getMsg());
                }
            }
        } else {
            if (Boolean.TRUE.equals(mainContract.getOriginalPriceInfo().isLevelDiscounted())) {
                throw new BadRequestException(CAR_SUBSCRIBE_LEVEL_NOT_MATCH_WITH_MAIN_CONTRACT.getMsg());
            }
        }
    }

    private boolean isSubscribeLevelMatch(Cars car, MainContract mainContract) {
        return car.getSubscribeLevel().equals(mainContract.getGivenCarLevel());
    }

    private boolean isDiscountLevelMatch(MainContract mainContract, SubscribeLevel carSubscribeLevel) {
        return mainContract.getGivenCarLevel().equals(subscribeLevelService.getDiscountLevelBySubscriptionLevel(carSubscribeLevel).getLevel());
    }

    /**
     * 訂單狀態改為取消
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders cancelOrder(Orders order) {
        if (order.getStatus() > BOOKING.getStatus()) {
            throw new SubscribeException(ORDER_CANCEL_FAIL);
        }
        order.setStage(Math.abs(order.getStage()) * -1);
        order.setStatus(CANCEL.getStatus());
        order.setIsUnpaid(false);
        return orderRepository.save(order);
    }

    /**
     * 取消訂單
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders cancelOrder(String orderNo, CancelRequest cancelRequest, String memberId) {
        invoiceServiceV2.cleanInvoiceCache(orderNo);
        // 作廢不給執行Transaction 因為已打API，故必須異動資料庫
        List<Invoices> invoicesList = invoiceServiceV2.getInvoice(orderNo).stream().filter(i -> i.getStatus().equals(InvoiceDefine.InvStatus.CREATE.name())).collect(Collectors.toList());
        if (!invoicesList.isEmpty()) {
            throw new SubscribeException(INVOICE_ALREADY_CREATED_CANCEL_ORDER_FAIL);
        }

        Orders order = getOrder(orderNo);
        Orders preOrder = orderRepository.getPreviousOrders(orderNo);
        int oriOrderStatus = order.getStatus();
        order = cancelOrder(order);
        // 紀錄續約狀態到上期訂單
        recordRenewTypeToPreOrder(orderNo, RenewType.CANCEL);

        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(order.getOrderNo());

        Cars car = carsService.findByPlateNo(order.getPlateNo());

        contractService.cancelContract(order.getContract());
        contractService.cancelMainContract(order.getContract().getMainContract());
        OrderPriceInfo cancelOrderPriceInfo = null;
        OrderPriceInfo securityDepositPriceInfo;

        CancellationPolicy originalCancellationPolicy = CancellationPolicy.of(CancellationPolicy.SubscribeOldCar.ofDays(0));
        if (oriOrderStatus >= BOOKING.getStatus()) {
            originalCancellationPolicy = PriceUtils.getCancelOrderLevel(order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDepositDate().toInstant(), true);
        }
        originalCancellationPolicy.calculateCancelPolicy(order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo().getPaidSecurityDeposit());

        int invoiceAmt = 0;
        if (cancelRequest.getInvoices() != null && !cancelRequest.getInvoices().isEmpty()) {
            for (InvoiceRequest invoice : cancelRequest.getInvoices()) {
                invoiceAmt += invoice.getAmount();
            }
        }

        // 若退款金額與發票金額不同則需檢查 若為新單或是
        if (originalCancellationPolicy.getLevel() > 1 && order.getIsNewOrder()) {
            securityDepositPriceInfo = orderPriceInfoList.stream().filter(o -> o.getCategory().equals(PriceInfoDefinition.PriceInfoCategory.SecurityDeposit)).findFirst().orElse(null);
            if (invoiceAmt != originalCancellationPolicy.getReceivableAmt()) {
                cancelOrderPriceInfo =
                    orderPriceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getCategory() == CancelBooking && Objects.equals(orderPriceInfo.getRefPriceInfoNo(), securityDepositPriceInfo.getId())).findAny()
                        .orElseThrow(() -> new BadRequestException("預設不可全額退保證金，需先送審"));
                if (!Objects.equals(cancelOrderPriceInfo.getInfoDetail().getIsAgree(), Boolean.TRUE)) {
                    throw new BadRequestException("需等待主管同意");
                }
            } else {
                cancelOrderPriceInfo = generateRefundOrderPriceInfo(order, CancelBooking, originalCancellationPolicy.getRefundAmt(), Optional.ofNullable(securityDepositPriceInfo).map(OrderPriceInfo::getId).orElse(null));
            }
        } else {
            securityDepositPriceInfo = null;
        }
        // 產生退款資訊
        List<OrderPriceInfo> paidOrderPriceInfos = orderPriceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getType() == Pay.getCode()).collect(Collectors.toList());
        for (OrderPriceInfo paidOrderPriceInfo : paidOrderPriceInfos) {
            // 因有保證金議價，已產生退款明細，不再次產生
            if (paidOrderPriceInfo.getCategory() == PriceInfoDefinition.PriceInfoCategory.SecurityDeposit && (cancelOrderPriceInfo != null || invoiceAmt > 0)) {
                continue;
            }
            priceInfoService.refundAll(paidOrderPriceInfo.getId(), CancelBooking);
        }
        // 清空折價費用
        List<OrderPriceInfo> discountOrderPriceInfos = orderPriceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getType() == Discount.getCode() && orderPriceInfo.getReceivedAmount() == 0).collect(Collectors.toList());
        for (OrderPriceInfo discountOrderPriceInfo : discountOrderPriceInfos) {
            discountOrderPriceInfo.setAmount(0);
        }

        if (cancelOrderPriceInfo != null && cancelOrderPriceInfo.getAmount() > 0) {
            orderPriceInfoRepository.save(cancelOrderPriceInfo);
            orderPriceInfoList = priceInfoService.getPriceInfosByOrder(order.getOrderNo());
        }

        // 模擬 收支登打
        List<AccountRecord> reqAccounts = getAccountRecords(order, orderPriceInfoList, true, cancelOrderPriceInfo);
        if (!reqAccounts.isEmpty()) {
            // 建立發票
            invoiceServiceV2.createInvoice(orderNo, Depart.name(), cancelRequest.getInvoices(), memberId);
        }
        order.setCancelDate(Instant.now());
        order.setCancelMemo(cancelRequest.getRemark());
        order.setCancelUser(memberId);

        orderRepository.save(order);

        // 若不為新單，且前約已還車，則發動保證金退款
        if (!order.getIsNewOrder() && (preOrder.getStatus() == CLOSE.getStatus() || preOrder.getStatus() == CLOSE_WITH_SUB.getStatus())) {
            refundSecurityDeposit(order.getContract().getMainContract());
        }

        // 釋放車輛
        carsService.releaseCar(order, car);

        // 沒有出車中的訂單，則自動撥車還車
        MainContract mainContract = mainContractRepository.getMainContractByNo(order.getContract().getMainContract().getMainContractNo(), true);
        if (mainContract.getContracts().stream().noneMatch(contract ->
            contract.getOrders().stream().anyMatch(orders ->
                orders.getStatus() == DEPART.getStatus()))) {

            // 若為自動撥車，則發動自動還車
            buChangeService.changeReturn(order, crsService.getCar(car.getPlateNo()), memberId, car);
            carsService.update(car);
            // 建立部門約
            order = generateCenterContract(order, car, true);
            // 通知營業取消長租合約
            notifyService.notifyContractCancel(order, car.getPlateNo(), car.getPlateNo(), order.getLrentalContractNo());
        }

        // 釋放車輛須放在自動撥車還車之後，因releaseCar非用Entity進行update，會導致Cars物件狀態在DB實際已異動，但又被回寫為初始值
        // 或重新寫一releaseCar為使用entity進行異動
        if (order.getIsNewOrder()) {
            rentalTaskService.deleteTask(order, TaskType.DEPART);
            carsService.updateLocationStationBasedOnSgType(mainContract, car);
        }

        AuthUser user = authServer.getUserWithRetry(order.getContract().getMainContract().getAcctId());

        orderRepository.save(order);
        notifyService.notifyCancelOrder(order, user);
        notifyToCService.notifyCancelOrder(order, user);
        try {
            checkoutService.orderRefundCheckOut(order, cancelOrderPriceInfo);
        } catch (Exception e) {
            log.error("登打失敗", e);
        }
        return order;
    }

    private OrderPriceInfo generateRefundOrderPriceInfo(Orders order, PriceInfoDefinition.PriceInfoCategory priceInfoCategory, int refundAmt, Integer refPriceInfoNo) {
        OrderPriceInfo refundOrderPriceInfo = new OrderPriceInfo();
        refundOrderPriceInfo.setReceivableDate(Instant.now());
        refundOrderPriceInfo.setLastPayDate(order.getExpectEndDate());
        refundOrderPriceInfo.setCategory(priceInfoCategory);
        refundOrderPriceInfo.setType(Refund.getCode());
        refundOrderPriceInfo.setAmount(refundAmt);
        refundOrderPriceInfo.setOrderNo(order.getOrderNo());
        refundOrderPriceInfo.setStage(1);
        refundOrderPriceInfo.setRefPriceInfoNo(refPriceInfoNo);
        return refundOrderPriceInfo;
    }

    /**
     * Step 1：決定法務事由
     * 逾期不還車 → Step 3
     * 已還車全車損 → Step 2
     * 已還車有欠款 → Step 2
     * 已還車有欠款且全車損 → Step 2
     * Step 2：確認還車人員
     * Step 3：決定沒收比例
     * 預設為 100%，可手動調整比例
     * 計算沒收金額
     * Step 4：開立發票
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void legalOperation(String orderNo, LegalOperationRequest legalOperationRequest, String memberId) {

        Orders order = getOrder(orderNo);
        MainContract mainContract = order.getContract().getMainContract();
        SecurityDepositInfo securityDepositInfo = mainContract.getOriginalPriceInfo().getSecurityDepositInfo();

        // 驗證訂單狀態: 出車中或已還車未結案 && 尚未產生續約訂單
        boolean isDepartOrder = DEPART.getStatus() == order.getStatus();
        boolean isArriveNoCloseOrder = ARRIVE_NO_CLOSE.getStatus() == order.getStatus();

        if ((!(isDepartOrder || isArriveNoCloseOrder)
            || (RenewType.RENEW.equals(order.getRenewType())
            || RenewType.AUTO_RENEW.equals(order.getRenewType())
            || order.getNextStageOrderNo() != null)) && !legalOperationRequest.getForceExecute()) {
            throw new SubscribeException(LEGAL_OPERATION_ORDER_CONSTRAINT);
        }

        LegalOperationReason legalOperationReason = legalOperationRequest.getReason();
        CarDropOffCompleteRequest dropOffRequest = new CarDropOffCompleteRequest();
        // 如果是已還車未結案，需額外檢查保證金，並取得還車人員編號
        if (isArriveNoCloseOrder) {
            if (securityDepositInfo.getPaidSecurityDeposit() <= 0
                || securityDepositInfo.getRefundSecurityDepositRequestDate() != null) {
                throw new SubscribeException(LEGAL_OPERATION_SECURITY_DEPOSIT_CONSTRAINT);
            }

            // 已還車未結案只能選擇 逾期不還車 以外的選項
            if (legalOperationReason == OVERDUE_NO_RETURN) {
                throw new SubscribeException(INVALID_LEGAL_OPERATION_REASON_FOR_ARRIVE_NO_CLOSE);
            }

            // 從已還車未結案訂單取得還車人員編號
            dropOffRequest.setReturnMemberId(order.getReturnMemberId());
        }
        // 由法務事由判斷是否還車
        boolean shouldReturnCar = Arrays.asList(ARRIVE_NO_CLOSE, CLOSE).contains(legalOperationReason.getOrderStatus());
        // 若應還車且非已還車未結案訂單，則提前驗證還車人員編號是否存在
        if (shouldReturnCar && !isArriveNoCloseOrder) {
            validateMemberExists(legalOperationRequest.getReturnMemberId());
        }

        // 若法務事由為「已還車但保證金轉收入」，法務作業前須帳平
        if (legalOperationReason == RETURNED_WITH_PAID && !paymentService.checkBalance(orderNo)) {
            throw new SubscribeException(ORDER_PRICE_INFO_AND_AMOUNT_NOT_BALANCE);
        }

        // 初始化沖銷 etag 金額
        int etagWrittenOffAmount = 0;
        // 驗證欲沖銷 etag 費用
        List<Integer> etagPriceInfoIdsToWriteOff = legalOperationRequest.getEtagPriceInfoIdsToWriteOff();
        List<OrderPriceInfo> etagPriceInfosBeforeDropOffCarConfirm = new ArrayList<>();
        PaymentInfo securityDepositPayment = paymentService.getSecurityDepositPaymentOrElseThrow(orderNo);
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(orderNo);
        if (!CollectionUtils.isEmpty(etagPriceInfoIdsToWriteOff)) {
            List<String> errorMessages = new ArrayList<>();

            // 1. 預先取得所有相關的 OrderPriceInfo 和 ETagInfo
            etagPriceInfosBeforeDropOffCarConfirm = priceInfoWrapper.getByCategoryType(ETag, Pay).getList();
            List<OrderPriceInfo> etagPriceInfosToWriteOff = etagPriceInfosBeforeDropOffCarConfirm.stream()
                .filter(opi -> !opi.isPaid() && etagPriceInfoIdsToWriteOff.contains(opi.getId()))
                .collect(Collectors.toList());
            Map<Integer, ETagInfo> etagInfoMap = etagInfoRepository.getETagInfosByOrderNo(orderNo).stream()
                .filter(etagInfo -> !etagInfo.isPaymentCompleted())
                .collect(Collectors.toMap(ETagInfo::getOrderPriceInfoId, Function.identity()));

            // 驗證 etagPriceInfosToWriteOff 數量是否正確
            if (CollectionUtils.isEmpty(etagPriceInfosToWriteOff) || etagPriceInfosToWriteOff.size() != etagPriceInfoIdsToWriteOff.size()) {
                errorMessages.add("ETag 訂單費用資訊中數量不符");
            }

            // 驗證 ETagInfo 數量是否與 etagPriceInfosToWriteOff 數量一致
            if (errorMessages.isEmpty() && (CollectionUtils.isEmpty(etagInfoMap) || etagInfoMap.size() != etagPriceInfosToWriteOff.size())) {
                errorMessages.add("ETagInfo 數量與 ETag 訂單費用資訊數量不符");
            }

            if (errorMessages.isEmpty()) {
                for (OrderPriceInfo priceInfo : etagPriceInfosToWriteOff) {
                    if (priceInfo.isPaid()) {
                        errorMessages.add(String.format("ETag 訂單費用資訊(id: %d)已付款", priceInfo.getId()));
                    }
                }

                // 2. 驗證每一個 etagPriceInfoId 對應的 ETagInfo
                for (Integer etagPriceInfoId : etagPriceInfoIdsToWriteOff) {
                    ETagInfo etagInfo = etagInfoMap.get(etagPriceInfoId);

                    if (etagInfo == null) {
                        errorMessages.add(String.format("ETagInfo(orderPriceInfoId: %d) 不存在", etagPriceInfoId));
                    }
                }
            }

            // 最後拋出 errorMessages
            if (!errorMessages.isEmpty()) {
                throw LogicException.of(PRECONDITION_FAILED, String.join(", ", errorMessages));
            }

            // 還車資料確認前先沖銷 etag 費用，同時計算沖銷 etag 金額總和
            etagWrittenOffAmount = etagPriceInfosToWriteOff.stream()
                .peek(priceInfo -> priceInfoService.processOrderPriceInfoPayment(priceInfo, securityDepositPayment))
                .mapToInt(PriceInfoInterface::getActualPrice)
                .sum();
            // etag 結單
            eTagService.close(order, mainContract.getPlateNo(), order.getDepartMemberId(), mainContract.getReturnStationCode(), true, true);
        }

        Cars car = carsService.findByPlateNo(mainContract.getPlateNo());

        if (isDepartOrder) {
            // 還車資料確認
            switch (legalOperationReason) {
                case OVERDUE_NO_RETURN:
                    order.setStatus(STOLEN.getStatus());
                    car.setCarStatus(CarDefine.CarStatus.Stolen.getCode());
                    break;
                case RETURNED_WITH_DAMAGE:
                case RETURNED_WITH_UNPAID:
                case RETURNED_WITH_UNPAID_AND_DAMAGE:
                case RETURNED_WITH_PAID:
                    order = checkIsUnpaid(order);
                    // 若沒有還車日期，或是有未繳款項，才執行還車資料確認
                    if (order.getEndDate() == null || order.getIsUnpaid()) {
                        CarDropOffRequest carDropOffRequest = getCarDropOffRequest(legalOperationRequest, order);
                        long diffDay = DateUtil.calculateDiffDate(carDropOffRequest.getReturnDate().toInstant(), order.getExpectEndDate(), DAYS);
                        // 還車資訊確認前驗證
                        validateDropOffCarConfirmPreConditions(carDropOffRequest, order, diffDay);
                        dropOffCarConfirm(order.getOrderNo(), carDropOffRequest, memberId);
                    }
                    break;
                default:
                    throw new SubscribeException(INVALID_LEGAL_OPERATION_REASON);
            }
        }

        // 還車資料確認後，確認最新 etag 費用數量與還車資料確認前是否一致
        if (!CollectionUtils.isEmpty(etagPriceInfoIdsToWriteOff)
            && priceInfoWrapper.getByCategoryType(ETag, Pay).getList().size() != etagPriceInfosBeforeDropOffCarConfirm.size()) {
            log.warn("有產生新的 ETag 訂單費用資訊");
        }

        // 已收保證金
        int paidSecurityDeposit = securityDepositInfo.getPaidSecurityDeposit();
        // 沒收租金金額(預設為 100% 已收保證金)
        Integer forfeitedForRentalAmount = Optional.ofNullable(legalOperationRequest.getForfeitedForRentalAmount()).orElse(paidSecurityDeposit);
        // 沒收金額 = 沒收租金金額 + 沖銷 etag 金額
        int forfeitedAmount = forfeitedForRentalAmount + etagWrittenOffAmount;
        // 若沒收金額是否大於已收保證金則報錯
        if (forfeitedAmount > paidSecurityDeposit) {
            throw LogicException.of(PRECONDITION_FAILED, "沒收金額總和大於已收保證金");
        }
        // 檢查是否有保證金需要沒收
        if (paidSecurityDeposit > 0 && forfeitedAmount > 0) {
            // 剩餘未沒收保證金金額，在未來會退款
            int refundSecurityDepositAmt = paidSecurityDeposit - forfeitedAmount;

            // 取得母單保證金付款 OrderPriceInfo
            OrderPriceInfo forfeitedDepositPriceInfo = getSecurityDepositOrderPriceInfo(mainContract);
            OrderPriceInfo priceInfoToBeRefunded = new OrderPriceInfo();
            if (refundSecurityDepositAmt == 0) {
                // 若全額沒收(refundSecurityDepositAmt=0),則將原保證金 OrderPriceInfo 的 category 改為 Others
                forfeitedDepositPriceInfo.setCategory(Others);
                forfeitedDepositPriceInfo.setAmount(forfeitedForRentalAmount);
                forfeitedDepositPriceInfo.setReceivedAmount(forfeitedForRentalAmount);
                PriceInfoDetail infoDetail = Optional.ofNullable(forfeitedDepositPriceInfo.getInfoDetail()).orElseGet(PriceInfoDetail::new);
                infoDetail.setReason("保證金轉租金");
                forfeitedDepositPriceInfo.setInfoDetail(infoDetail);
                orderPriceInfoRepository.save(forfeitedDepositPriceInfo);
            } else {
                // 保證金部分沒收
                // 從原本 SecurityDeposit OrderPriceInfo clone 一個新的 OrderPriceInfo，新的 OrderPriceInfo id set null，amount 和 receivedAmount 改為剩餘未沒收保證金
                // 原本的 OrderPriceInfo：amount 和 receivedAmount 改為沒收租金金額，category 改為 Others，註記 infoDetail.reason 為 "保證金轉租金"
                BeanUtils.copyProperties(forfeitedDepositPriceInfo, priceInfoToBeRefunded);
                priceInfoToBeRefunded.setId(null);
                priceInfoToBeRefunded.setAmount(refundSecurityDepositAmt);
                priceInfoToBeRefunded.setReceivedAmount(refundSecurityDepositAmt);

                forfeitedDepositPriceInfo.setAmount(forfeitedForRentalAmount);
                forfeitedDepositPriceInfo.setReceivedAmount(forfeitedForRentalAmount);
                forfeitedDepositPriceInfo.setCategory(Others);
                PriceInfoDetail infoDetail = Optional.ofNullable(forfeitedDepositPriceInfo.getInfoDetail()).orElseGet(PriceInfoDetail::new);
                infoDetail.setReason("保證金轉租金");
                forfeitedDepositPriceInfo.setInfoDetail(infoDetail);
                orderPriceInfoRepository.saveAll(Arrays.asList(priceInfoToBeRefunded, forfeitedDepositPriceInfo));
            }

            // 收支登打、開立發票
            accountSettlementForLegalOperation(order, forfeitedDepositPriceInfo, securityDepositPayment, priceInfoToBeRefunded, legalOperationRequest.getMemo(), memberId);
        }

        if (shouldReturnCar) {
            if (dropOffRequest.getReturnMemberId() == null) {
                dropOffRequest.setReturnMemberId(legalOperationRequest.getReturnMemberId());
            }
            // 執行還車邏輯
            dropOffCarForLegalOperation(order, dropOffRequest, legalOperationRequest.getReason(), memberId);
        }

        order.setLegalOperationReason(legalOperationReason.getCode());
        orderRepository.save(order);
        carsService.update(car);

        // 新增註解
        String remarkContent = String.format("完成法務作業 (事由：%s)，保證金%s %s",
            legalOperationReason.getDescription(),
            legalOperationReason == RETURNED_WITH_PAID ? "轉收入" : "沒收",
            String.format("$%,d (租金 $%,d + etag $%,d)", forfeitedAmount, forfeitedForRentalAmount, etagWrittenOffAmount));
        addRemark(orderNo, remarkContent, memberId);

        if (legalOperationReason.isCreateLrentalContractNeeded()) {
            // 建立部門約
            order = generateCenterContract(order, car, true);
            orderRepository.save(order);
        }
    }

    /**
     * 取消訂單前處理，將發票作廢/折讓，並處理退款
     * 該處不使用transaction，因為已打API，故必須異動資料庫
     * 也避免拿invoiceServiceV2.getInvoice(orderNo)來取得發票資訊，因為在transaction中，拿到異動前的資料，因為會有快取問題
     */
    public void processBeforeCancelOrder(String orderNo, String memberId, boolean forceRemitRefund) {
        try {
            Orders order = getOrder(orderNo);
            List<Account> accounts = paymentService.getAccountsByOrders(orderNo);
            if (!forceRemitRefund && accounts.stream().anyMatch(account -> account.getAccountType() == AccountType.Remit)) {
                throw new SubscribeException(ORDER_CAN_NOT_CANCEL_CAUSE_REMIT);
            }
            // 作廢不給執行Transaction 因為已打API，故必須異動資料庫
            List<Invoices> invoicesList = invoiceServiceV2.getInvoice(orderNo).stream().filter(i -> i.getStatus().equals(InvoiceDefine.InvStatus.CREATE.name())).collect(Collectors.toList());
            if (!invoicesList.isEmpty()) {
                for (Invoices invoices : invoicesList) {
                    invoiceServiceV2.updateInvoice(invoices, "取消訂單退款", memberId);
                }
                invoiceServiceV2.cleanInvoiceCache(orderNo);
                // 作廢發票後，redis需重新取得發票資訊
                invoiceServiceV2.getInvoice(orderNo);
            }
            cancelOrderAutoRefund(orderNo, memberId, accounts, order);
        } finally {
            // 清除訂單快取
            invoiceServiceV2.cleanInvoiceCache(orderNo);
        }
    }

    /**
     * 取消訂單自動退款
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void cancelOrderAutoRefund(String orderNo, String memberId, List<Account> accounts, Orders order) {
        if (accounts.isEmpty() || accounts.stream().mapToInt(Account::getAmount).sum() == 0) {
            return;
        }

        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(order.getOrderNo());

        // 產生退款資訊
        List<OrderPriceInfo> paidOrderPriceInfos = orderPriceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getType() == Pay.getCode()).collect(Collectors.toList());
        for (OrderPriceInfo paidOrderPriceInfo : paidOrderPriceInfos) {
            // 因有保證金議價，已產生退款明細，不再次產生
            if (paidOrderPriceInfo.getCategory() == PriceInfoDefinition.PriceInfoCategory.SecurityDeposit) {
                continue;
            }
            priceInfoService.refundAll(paidOrderPriceInfo.getId(), CancelBooking);
        }

        List<AccountRecord> reqAccounts = getAccountRecords(order, orderPriceInfoList, false, null);
        if (!reqAccounts.isEmpty()) {
            reqAccounts.forEach(accountRecord -> accountRecord.setRefundAmount(accountRecord.getAmount()));
            PaymentRequest paymentRequest = new PaymentRequest();
            paymentRequest.setStationCode(subscribeStationCode);
            paymentRequest.setAccountRecords(reqAccounts);
            AccountSettlementRequest accountSettlementRequest = new AccountSettlementRequest();
            accountSettlementRequest.setPaymentRequest(paymentRequest);
            paymentService.accountSettlement(accountSettlementRequest, orderNo, memberId);
        }
    }

    public OrderPriceInfo getSecurityDepositOrderPriceInfo(MainContract mainContract) {
        Set<String> ordersNos = getRelatedOrderNos(mainContract);
        List<OrderPriceInfo> paidSecurityDepositPriceInfos = getPaidSecurityDepositPriceInfos(new ArrayList<>(ordersNos));
        if (paidSecurityDepositPriceInfos.isEmpty()) {
            throw new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND);
        }
        if (paidSecurityDepositPriceInfos.size() > 1) {
            mattermostServer.notify("訂單因有換車，保證金付款資訊超過一筆，須人工處理", new SingletonMap<>("保證金付款資訊", paidSecurityDepositPriceInfos), null);
            throw new SubscribeException(MANUAL_REFUND_SECURITY_DEPOSIT_FOR_CHANGE_CAR);
        }
        return paidSecurityDepositPriceInfos.get(0);
    }

    /**
     * 收支登打、開立發票
     * 若 order 為續約訂單，須先取得母單來做以下操作
     */
    private void accountSettlementForLegalOperation(Orders order, OrderPriceInfo forfeitedPriceInfo, PaymentInfo securityDepositPayment,
                                                    OrderPriceInfo priceInfoToBeRefunded, String memo, String memberId) {

        // 若 order 為續約訂單，須先取得母單
        Orders orderForSettlement = order;
        if (!order.getIsNewOrder()) {
            orderForSettlement = forfeitedPriceInfo.getOrder();
        }

        AccountSettlementRequest accountSettlementRequest = new AccountSettlementRequest();
        InvoiceNewRequest invoiceNewRequest = new InvoiceNewRequest();
        invoiceNewRequest.setStationCode(subscribeStationCode);
        invoiceNewRequest.setPayFor(PayFor.Return.name());
        InvoiceRequest invoiceRequest = new InvoiceRequest();
        invoiceRequest.setInvoice(orderForSettlement.getInvoice());
        invoiceRequest.setAmount(forfeitedPriceInfo.getReceivedAmount());
        invoiceRequest.setRefPriceInfoIds(Collections.singletonList(forfeitedPriceInfo.getId()));
        invoiceRequest.setMemo(memo);
        invoiceNewRequest.setInvoices(Collections.singletonList(invoiceRequest));
        accountSettlementRequest.setInvoiceNewRequest(invoiceNewRequest);

        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setStationCode(subscribeStationCode);
        String orderNo = orderForSettlement.getOrderNo();
        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrderAndAcctId(
            orderNo,
            orderForSettlement.getContract().getMainContract().getAcctId()
        );

        List<AccountRecord> accountRecords = getAccountRecords(orderForSettlement, orderPriceInfoList, false, null);
        AccountRecord accountRecord = buildAccountRecord(forfeitedPriceInfo.getReceivedAmount(), orderNo, securityDepositPayment, Depart);
        accountRecords.add(accountRecord);

        paymentRequest.setAccountRecords(accountRecords);
        accountSettlementRequest.setPaymentRequest(paymentRequest);
        paymentService.accountSettlementForLegalOperation(accountSettlementRequest, orderNo, memberId);
        paymentService.payAndDiscountBalance(orderNo);
        checkoutService.orderRefundCheckOut(orderForSettlement, priceInfoToBeRefunded, true);
    }

    public AccountRecord buildAccountRecord(int amount, String orderNo, PaymentInfo paymentInfo, PayFor payFor) {
        AccountRecord accountRecord = new AccountRecord();
        accountRecord.setAmount(amount);
        accountRecord.setOriginalAmount(amount);
        accountRecord.setOrderNo(orderNo);
        accountRecord.setAccountType(paymentInfo.getAccountType());
        accountRecord.setStationCode(subscribeStationCode);
        accountRecord.setTradeId(paymentInfo.getTradeId());
        accountRecord.setChargeType(paymentInfo.getChargeType());
        accountRecord.setCardNumber(paymentInfo.getCardNumber());
        accountRecord.setAuthCode(paymentInfo.getAuthCode());
        accountRecord.setPayFor(payFor);
        accountRecord.setTransactionNumber(paymentInfo.getTransactionNumber());
        return accountRecord;
    }

    /**
     * 檢查是否有未付款項
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders checkIsUnpaid(Orders orders) {
        return priceInfoService.checkIsUnpaid(orders);
    }

    /**
     * 取得前一筆訂單
     */
    public Orders getPreviousOrders(String orderNo) {
        return orderRepository.getPreviousOrders(orderNo);
    }


    /**
     * 自動收支燈打與開立發票
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void autoRecordAccountsCreateInvoice(Orders order, List<OrderPriceInfo> orderPriceInfoList) {
        autoRecordAccounts(order);
        invoiceServiceV2.autoCreateInvoice(order, orderPriceInfoList);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void autoRecordAccounts(Orders order) {
        //收支登打
        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrderAndAcctId(order.getOrderNo(), order.getContract().getMainContract().getAcctId());
        List<AccountRecord> reqAccounts = getAccountRecords(order, orderPriceInfoList, false, null);
        if (!reqAccounts.isEmpty()) {
            PaymentRequest paymentRequest = new PaymentRequest();
            paymentRequest.setStationCode(subscribeStationCode);
            paymentRequest.setAccountRecords(reqAccounts);
            paymentService.recordAccounts(paymentRequest, order.getOrderNo());
        }
    }

    /**
     * 自動產生收支燈打資料
     *
     * @NOTICE 匯款須先到
     */
    private List<AccountRecord> getAccountRecords(Orders order, List<OrderPriceInfo> orderPriceInfoList, boolean isCancel, OrderPriceInfo cancelOrderPriceInfo) {
        List<AccountRecord> reqAccounts = Lists.newArrayList();
        List<Account> accounts = paymentService.getAccountsByOrders(order.getOrderNo());
        Map<String, Account> creditAccountMap = accounts.stream().filter(account -> !account.isDeleted() && account.getAccountType() == AccountType.Credit)
            .collect(Collectors.toMap(Account::getTradeId, account -> account));
        Map<String, Account> remitAccountMap = accounts.stream().filter(account -> !account.isDeleted() && account.getAccountType() == AccountType.Remit)
            .collect(Collectors.toMap(acc -> acc.getRemitNo().toString(), account -> account));
        for (PaymentInfo paymentInfo : paymentService.getPaymentsByOrder(order.getOrderNo()).stream().filter(p -> p.getPaymentCategory() == PayAuth).sorted(Comparator.comparing(PaymentInfo::getAmount).reversed()).collect(Collectors.toList())) {
            AccountRecord reqAccount = new AccountRecord();
            if (!creditAccountMap.containsKey(paymentInfo.getTradeId())) {
                reqAccount.setOrderNo(order.getOrderNo());
                reqAccount.setOriginalAmount(paymentInfo.getAmount());
                reqAccount.setAccountType(paymentInfo.getAccountType());
                reqAccount.setStationCode(subscribeStationCode);
                reqAccount.setTradeId(paymentInfo.getTradeId());
                if (paymentInfo.getChargeType() == null) {
                    if (paymentInfo.getAcquirer() == Acquirer.TW_TAISHIN) {
                        reqAccount.setChargeType(ChargeType.Tappay_TAISHIN.getCreditBankAuto());
                    } else if (paymentInfo.getAcquirer() == Acquirer.TW_CTBC) {
                        reqAccount.setChargeType(ChargeType.Tappay_CTBC.getCreditBankAuto());
                    } else if (paymentInfo.getAcquirer() == Acquirer.TW_NCCC) {
                        reqAccount.setChargeType(ChargeType.Tappay_NCCC.getCreditBankAuto());
                    }
                } else {
                    reqAccount.setChargeType(paymentInfo.getChargeType());
                }
                reqAccount.setCardNumber(paymentInfo.getCardNumber());
                reqAccount.setAuthCode(paymentInfo.getAuthCode());
                reqAccount.setPayFor(paymentInfo.getPayFor());
                reqAccount.setTotalRefundAmount(0);
                reqAccount.setTransactionNumber(paymentInfo.getTransactionNumber());
            } else {
                Account account = creditAccountMap.get(paymentInfo.getTradeId());
                BeanUtils.copyProperties(account, reqAccount);
                reqAccount.setOriginalAmount(account.getAmount() + account.getTotalRefundAmount());
                reqAccount.setTransactionNumber(account.getTransactionNumber());
            }
            if (isCancel) {
                // 非保證金收款全額退
                if (paymentInfo.getPayFor() != SecurityDeposit) {
                    reqAccount.setRefundAmount(paymentInfo.getAmount());
                } else {
                    // 保證金退款
                    SecurityDepositInfo securityDepositInfo = order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo();
                    if (cancelOrderPriceInfo != null) {
                        reqAccount.setRefundAmount(cancelOrderPriceInfo.getAmount());
                        securityDepositInfo.getSecurityDepositPaid(-1 * cancelOrderPriceInfo.getAmount());
                    } else {
                        reqAccount.setRefundAmount(securityDepositInfo.getPaidSecurityDeposit());
                        securityDepositInfo.getSecurityDepositPaid(-1 * securityDepositInfo.getPaidSecurityDeposit());
                    }
                }
            } else {
                // 登打收入
                // 略過保證金
                if (paymentInfo.getPayFor() != SecurityDeposit) {
                    // 計算退款金額
                    // 先找尋有付款紀錄的明細，再查詢有哪些退款明細是對應到相對應的付款明細，去計算應退總額
                    int refund = orderPriceInfoList.stream().filter(refundOrderPriceInfo -> refundOrderPriceInfo.getType() == Refund.getCode()
                            && orderPriceInfoList.stream().filter(orderPriceInfo -> paymentInfo.getPaymentId().equals(orderPriceInfo.getPaymentId()))
                            .map(OrderPriceInfo::getId).collect(Collectors.toList()).contains(refundOrderPriceInfo.getId()))
                        .mapToInt(OrderPriceInfo::getAmount).sum();
                    reqAccount.setRefundAmount(refund);
                    // 減去Etag費用
                    if (reqAccount.getId() == null) {
                        int etagAmt = orderPriceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getCategory() == ETag
                            && paymentInfo.getPaymentId().equals(orderPriceInfo.getPaymentId())).mapToInt(orderPriceInfo -> (orderPriceInfo.getType() == Pay.getCode() ? 1 : -1) * orderPriceInfo.getReceivedAmount()).sum();
                        reqAccount.setOriginalAmount(reqAccount.getOriginalAmount() - etagAmt);
                    }
                } else {
                    continue;
                }
            }
            log.info("account:{}", new Gson().toJson(reqAccount));
            reqAccounts.add(reqAccount);
        }

        // 可能是匯款
        if (!remitAccountMap.isEmpty() && reqAccounts.isEmpty()) {
            reqAccounts.addAll(remitAccountMap.values().stream().map(acc -> {
                AccountRecord reqAccount = new AccountRecord();
                BeanUtils.copyProperties(acc, reqAccount);
                reqAccount.setOriginalAmount(acc.getAmount() + acc.getTotalRefundAmount());
                return reqAccount;
            }).collect(Collectors.toList()));
        }
        return reqAccounts;
    }

    /**
     * 訂單狀態改為已付款
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void bookingOrder(Orders order) {
        if (order.getStatus() > CREDITED.getStatus()) {
            throw new SubscribeException(ORDER_BOOKING_FAIL);
        }
        order.setSecurityDepositDate(Instant.now());
        order.setStatus(BOOKING.getStatus());
        orderRepository.save(order);
        eventPublisher.publishEvent(new OrderBookingEvent(this, order, order.getMemberId()));
    }

    /**
     * 保證金付款後鎖車
     */
    @Transactional
    public void lockCarAfterOrderPay(Orders order, Cars cars) {

        MainContract mainContract = order.getContract().getMainContract();

        // 虛擬車可以一直被定車
        if (!cars.isVirtualCar()) {
            try {
                if (!CarDefine.CarStatus.Free.getCode().equals(cars.getCarStatus()) && !mainContract.getMainContractNo().equals(cars.getBookingOrderNo())) {
                    throw new SubscribeException(NOT_EMPTY_CAR);
                }
                if (!Objects.equals(cars.getLaunched(), CarDefine.Launched.open) && !Objects.equals(cars.getLaunched(), CarDefine.Launched.close)) {
                    throw new SubscribeException(NOT_BUSINESS_CAR);
                }
            } catch (SubscribeException e) {
                self.carLockedAndOrderCancel(order, e);
                throw e;
            }
            carsService.lockCar(mainContract.getPlateNo(), CarDefine.CarStatus.Subscribed, mainContract.getMainContractNo());
        }
    }

    /**
     * 車輛鎖定後訂單取消，避免應為throw Exception而導致訂單異動roll back所以改用new required transaction
     */
    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRES_NEW)
    public void carLockedAndOrderCancel(Orders order, SubscribeException e) {
        if (Objects.equals(e.getCode(), NOT_EMPTY_CAR) || Objects.equals(e.getCode(), NOT_BUSINESS_CAR)) {
            order.setCancelDate(Instant.now());
            order.setCancelMemo(e.getReason());
            cancelOrder(order);
            contractService.cancelContract(order.getContract());
            contractService.cancelMainContract(order.getContract().getMainContract());
            List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(order.getOrderNo());
            orderPriceInfoList.forEach(opi -> priceInfoService.refundAll(opi.getId(), CancelBooking));
            notifyToCService.notifyCancelOrder(order, authServer.getUserWithRetry(order.getContract().getMainContract().getAcctId()));
        }
        updateOrder(order);
    }

    /**
     * 處理不需要自動授信的情況
     *
     * @param order                  訂單
     * @param memberId               工號
     * @param autoCreditBypassReason 不需要自動授信的原因
     */
    public void handleAutoCreditBypass(Orders order, String memberId, String autoCreditBypassReason) {
        // 記錄不需要自動授信的原因
        order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
            .updateAutoCredit(CreditRemarkType.AUTO_CREDIT_BYPASS, CreditMechanismType.NONE, Collections.singletonList(autoCreditBypassReason)));
        orderRepository.save(order);

        // 直接進行人工授信通過
        CreditApprovalRequest creditApprovalRequest = new CreditApprovalRequest(autoCreditBypassReason);
        approveCredit(order, memberId, creditApprovalRequest);
    }

    /**
     * 自動授信
     *
     * @param order    訂單
     * @param user     使用者
     * @param isNotify 是否通知
     * @param memberId 工號
     */
    public void autoCredit(Orders order, AuthUser user, boolean isNotify, String memberId) {
        // 執行自動授信並取得結果
        boolean isAutoCreditPass = authServer.doAutoCredit(order, user);
        AutoCreditInfo autoCreditInfo = order.getCreditInfo().getAutoCreditInfo().get(order.getCreditInfo().getAutoCreditInfo().size() - 1);
        orderRepository.save(order);

        // 處理特殊情況: 自動授信通過但標記為審核失敗
        if (isAutoCreditPass && autoCreditInfo.getCreditRemarkType() == CreditRemarkType.CREDIT_FAIL) {
            updateOrderForCreditReject(order);
        }

        // 判斷是否需要人工授信
        boolean needManualCredit = isManualCredit(order, user);

        // 處理自動授信結果
        if (!isAutoCreditPass) {
            handleFailedAutoCredit(order, user, isNotify, needManualCredit);
        } else {
            handleSuccessfulAutoCredit(order, user, isNotify, needManualCredit, memberId);
        }

        // 紀錄續約狀態到上期訂單
        recordRenewTypeToPreOrder(order.getOrderNo(), order.getStatus() == CREDIT_REJECT.getStatus() ? RenewType.CANCEL : RenewType.RENEW);
    }

    /**
     * 更新訂單狀態為審核失敗
     */
    private void updateOrderForCreditReject(Orders order) {
        order.setStage(Math.abs(order.getStage()) * -1);
        order.setIsUnpaid(false);
        order.setStatus(CREDIT_REJECT.getStatus());
        orderRepository.save(order);
    }

    /**
     * 處理自動授信未通過的情況
     */
    private void handleFailedAutoCredit(Orders order, AuthUser user, boolean isNotify, boolean needManualCredit) {
        if (!needManualCredit) {
            // 不通過且不須人工授信(因可能timeout或其他因素導致須重新授信或人工授信)
            updateOrderForCreditReject(order);
            contractService.cancelContract(order.getContract());
            contractService.cancelMainContract(order.getContract().getMainContract());
            if (isNotify) {
                notifyToCService.notifyCreditFail(order, user);
            }
            sensorWebhookService.sendCreditEvent(order, user);
        } else {
            // 需要人工授信
            if (isNotify) {
                notifyService.notifyCreditDemand(order, user);
            }
        }
    }

    /**
     * 處理自動授信通過的情況
     */
    private void handleSuccessfulAutoCredit(Orders order, AuthUser user, boolean isNotify, boolean needManualCredit, String memberId) {
        // 自動授信通過但仍需人工授信的情況
        if (!needManualCredit) {
            // 設定訂單狀態為已授信
            order.setStatus(CREDITED.getStatus());
            // 異動保證金可繳款期限
            priceInfoService.updateSecurityDepositLastPayDate(order.getOrderNo());

            // 依據是否為新訂單決定後續處理
            if (!order.getIsNewOrder()) {
                // 續約訂單處理
                order.setStatus(BOOKING.getStatus());
                eventPublisher.publishEvent(new OrderBookingEvent(this, order, memberId));
                if (isNotify) {
                    notifyService.notifyRenewConfirm(order, user);
                    notifyToCService.notifyRenewConfirm(order, user);
                }
            } else if (isNotify && order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit() > 0) {
                // 新訂單處理且方案保證金大於 0
                notifyToCService.notifyCreditSuccess(order, user);
            }

            // 發送事件並儲存
            sensorWebhookService.sendCreditEvent(order, user);
            orderRepository.save(order);
            eventPublisher.publishEvent(new OrderCreditApprovedEvent(this, order.getContract().getMainContract().getAcctId()));
        } else if (isNotify) {
            // 自動授信通過但仍需人工授信的情況
            notifyService.notifyCreditDemand(order, user);
        }
    }

    /**
     * 重新自動授信
     */
    public Orders retryAutoCredit(String orderNo, String memberId) {
        Orders orders = getOrder(orderNo);
        AuthUser user = authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId());
        if (orders.getStatus() != CREDIT_PENDING.getStatus()) {
            throw new SubscribeException(NOT_CREDIT_ORDER);
        }
        if (Optional.ofNullable(orders.getCreditInfo()).map(CreditInfo::getManualCreditInfo).isPresent()) {
            throw new SubscribeException(MANUAL_CREDIT_EVER);
        }
        //過濾需有SYSTEM_CREDIT_FAIL或是AutoCreditInfo為null才可進行授信，
        boolean canBeCredit = orders.getCreditInfo() == null || CollectionUtils.isEmpty(orders.getCreditInfo().getAutoCreditInfo())
            || orders.getCreditInfo().getAutoCreditInfo().stream().anyMatch(autoCreditInfo -> autoCreditInfo.getCreditRemarkType() == CreditRemarkType.SYSTEM_CREDIT_FAIL);
        if (canBeCredit) {
            autoCredit(orders, user, true, memberId);
            MainContract mainContract = orders.getContract().getMainContract();
            if (orders.getStatus() == BOOKING.getStatus()) {
                if (!orders.getIsNewOrder()) {
                    Orders preOrder = getPreviousOrders(orderNo);
                    createLrentalContractAfterRenew(configService.getSubscribeConfig().getSubscribeDefaultMemberId(), preOrder, orders);

                    // 0元保證金設定保證金付款時間
                } else if (mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit() == 0) {
                    mainContract.getOriginalPriceInfo().getSecurityDepositInfo().setSecurityDepositDate(new Date());
                    // 鎖車
                    Cars mainCar = carsService.findByPlateNo(mainContract.getPlateNo());
                    lockCarAfterOrderPay(orders, mainCar);
                    orderRepository.save(orders);
                    mainContractRepository.save(mainContract);
                }
            } else if (CREDITED.getStatus() == orders.getStatus()) {
                List<City> cities = goSmartServer.getCityArea();
                ContractCustomerCreateRequest request = new ContractCustomerCreateRequest(orders, user, cities, configService.getLRentalConfig());
                lrentalServer.createUserToDA71(request, memberId);
            }
        } else {
            throw new SubscribeException(NOT_CREDIT_ORDER);
        }

        return orders;
    }

    /**
     * 是否人工授信
     */
    boolean isManualCredit(Orders order, AuthUser user) {

        if (order.getStatus() != CREDIT_PENDING.getStatus()) {
            log.error("{},orderStatus:{},orderNo:{}", NOT_CREDIT_ORDER, order.getStatus(), order.getOrderNo());
            throw new SubscribeException(NOT_CREDIT_ORDER);
        }

        if (order.getCreditInfo() == null
            || order.getCreditInfo().getAutoCreditInfo() == null
            || order.getCreditInfo().getAutoCreditInfo().isEmpty()) {
            throw new SubscribeException(NO_CREDIT_EVER);
        }

        AutoCreditInfo autoCreditInfo = order.getCreditInfo().getAutoCreditInfo().stream()
            .max(Comparator.comparing(AutoCreditInfo::getCreditDate))
            .orElseThrow(() -> new SubscribeException(NO_CREDIT_EVER));

        boolean isManualCredit = CreditRemarkType.isManualCredit(autoCreditInfo.getCreditRemarkType());

        if (autoCreditInfo.getCreditRemarkType() == CreditRemarkType.AUTO_CREDIT_SUCCESS) {
            SubscribeLevel subscribeLevel = subscribeLevelService.findByLevel(order.getContract().getMainContract().getGivenCarLevel());

            if (!subscribeLevel.isAutoCredit()) {
                autoCreditInfo.setCreditRemarkType(CreditRemarkType.HIGH_PRICE);
                return true;
            }

            long activeOrderCount = orderRepository.getOrdersByAcctIds(Collections.singletonList(user.getAcctId()))
                .stream()
                .filter(o -> o.getStatus().equals(DEPART.getStatus()))
                .count();

            if (activeOrderCount >= 3) {
                autoCreditInfo.setCreditRemarkType(CreditRemarkType.ORDER_COUNT_FAIL);
                return true;
            }
        }

        return isManualCredit;
    }

    private void isMvdisNoPay(AutoCreditInfo autoCreditInfo) {
        if (autoCreditInfo.getCreditRemarkType() == CreditRemarkType.CREDIT_FAIL
            && autoCreditInfo.getAutoCreditFailMessages().size() == 1
            && autoCreditInfo.getAutoCreditFailMessages().get(0).contains("罰單")) {
            autoCreditInfo.setCreditRemarkType(CreditRemarkType.AUTO_CREDIT_FAIL);
        }

    }

    /**
     * 人工授信通過
     */
    public Orders approveCredit(String orderNo, String memberId, CreditApprovalRequest creditApprovalRequest) {
        // 原訂單資料 from DB
        Orders order = getOrder(orderNo);
        return approveCredit(order, memberId, creditApprovalRequest);
    }

    private Orders approveCredit(Orders order, String memberId, CreditApprovalRequest creditApprovalRequest) {
        // 狀態檢查
        if (order.getStatus() != CREDIT_PENDING.getStatus()) {
            throw new SubscribeException(NOT_CREDIT_ORDER);
        }
        List<MemberInfo> memberInfos = authorityServer.getMemberInfos(memberId);
        if (memberInfos.size() == 0) {
            throw new SubscribeException(MEMBER_INFO_NOT_FOUND);
        }
        AuthUser user = authServer.getUserWithRetry(order.getContract().getMainContract().getAcctId());
        List<City> cities = goSmartServer.getCityArea();
        MemberInfo memberInfo = memberInfos.get(0);
        //寫入授信通過審核人員
        Auditor auditor = new Auditor(memberInfo.getMemberName(), memberInfo.getMemberId(), memberInfo.getEmail());
        order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
            .update(auditor, creditApprovalRequest.getRemark(), CreditRemarkType.MANUAL_PASS));
        order.setStatus(CREDITED.getStatus());
        // 紀錄續約狀態到上期訂單
        recordRenewTypeToPreOrder(order.getOrderNo(), RenewType.RENEW);
        // 異動保證金可繳款期限
        priceInfoService.updateSecurityDepositLastPayDate(order.getOrderNo());
        priceInfoService.checkIsUnpaid(order);
        // 發繳保證金通知
        // 異動保證金可繳款期限
        priceInfoService.updateSecurityDepositLastPayDate(order.getOrderNo());
        if (!order.getIsNewOrder()) {
            order.setStatus(BOOKING.getStatus());
            eventPublisher.publishEvent(new OrderBookingEvent(this, order, memberId));
            notifyService.notifyRenewConfirm(order, user);
            notifyToCService.notifyRenewConfirm(order, user);
            Orders preOrder = getPreviousOrders(order.getOrderNo());
            createLrentalContractAfterRenew(configService.getSubscribeConfig().getSubscribeDefaultMemberId(), preOrder, order);
        } else {
            ContractCustomerCreateRequest request = new ContractCustomerCreateRequest(order, user, cities, configService.getLRentalConfig());
            lrentalServer.createUserToDA71(request, memberId);
            MainContract mainContract = order.getContract().getMainContract();
            if (mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit() == 0) {
                order.setStatus(BOOKING.getStatus());
                eventPublisher.publishEvent(new OrderBookingEvent(this, order, memberId));
                mainContract.getOriginalPriceInfo().getSecurityDepositInfo().setSecurityDepositDate(new Date());
                // 鎖車
                Cars mainCar = carsService.findByPlateNo(mainContract.getPlateNo());
                lockCarAfterOrderPay(order, mainCar);
                mainContractRepository.save(mainContract);
                // 寄送通知
                notifyService.notifyPaySecurityDepositSuccess(order, user);
                notifyToCService.notifySecurityDepositPaid(order, user);
            } else {
                notifyToCService.notifyCreditSuccess(order, user);
            }
        }
        sensorWebhookService.sendCreditEvent(order, user);
        eventPublisher.publishEvent(new OrderCreditApprovedEvent(this, order.getContract().getMainContract().getAcctId()));
        return orderRepository.save(order);
    }

    /**
     * 人工授信不通過
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders rejectCredit(CloseRequest closeRequest, String orderNo, String memberId) {
        // 原訂單資料 from DB
        Orders order = getOrder(orderNo);
        // 狀態檢查
        if (order.getStatus() != CREDIT_PENDING.getStatus()) {
            throw new SubscribeException(NOT_CREDIT_ORDER);
        }
        List<MemberInfo> memberInfos = authorityServer.getMemberInfos(memberId);
        if (memberInfos.size() == 0) {
            throw new SubscribeException(MEMBER_INFO_NOT_FOUND);
        }
        AuthUser user = authServer.getUserWithRetry(order.getContract().getMainContract().getAcctId());
        MemberInfo memberInfo = memberInfos.get(0);
        //寫入授信通過審核人員
        Auditor auditor = new Auditor(memberInfo.getMemberName(), memberInfo.getMemberId(), memberInfo.getEmail());
        order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
            .update(auditor, closeRequest.getRemark(), CreditRemarkType.MANUAL_FAIL));
        order.setStatus(CREDIT_REJECT.getStatus());
        order.setStage(-1 * Math.abs(order.getStage()));
        // 紀錄續約狀態到上期訂單
        recordRenewTypeToPreOrder(order.getOrderNo(), RenewType.PENDING);
        order.setRenewType(RenewType.CANCEL);
        order.setIsUnpaid(false);
        contractService.cancelContract(order.getContract());
        contractService.cancelMainContract(order.getContract().getMainContract());
        notifyToCService.notifyCreditFail(order, user);
        Cars cars = carsService.findByPlateNo(order.getPlateNo());
        if (cars == null) {
            throw new SubscribeException(CAR_NOT_FOUND);
        }
        if (order.getIsNewOrder()) {
            carsService.releaseCar(order, cars);
        } else {
            MainContract mainContract = mainContractRepository.getMainContractByNo(order.getContract().getMainContract().getMainContractNo(), true);
            Orders departOrders =
                mainContract.getContracts().stream().map(c -> c.getOrders().stream().filter(o -> o.getStatus() < CLOSE.getStatus() && !o.getOrderNo().equals(order.getOrderNo())).findAny().orElse(null)).filter(Objects::nonNull).findAny().orElse(null);
            if (departOrders == null) {
                carsService.releaseCar(order, cars);
                refundSecurityDeposit(order.getContract().getMainContract());
            }
        }
        sensorWebhookService.sendCreditEvent(order, user);
        return orderRepository.save(order);
    }

    /**
     * 由訂單建立71
     */
    public void addDa71ByOrder(String orderNo, String memberId) {
        Orders order = getOrder(orderNo);
        AuthUser authUser = authServer.getUserWithRetry(order.getContract().getMainContract().getAcctId());
        List<City> cities = goSmartServer.getCityArea();
        ContractCustomerCreateRequest request = new ContractCustomerCreateRequest(order, authUser, cities, configService.getLRentalConfig());
        lrentalServer.createUserToDA71(request, StringUtils.isBlank(memberId) ? configService.getSubscribeConfig().getSubscribeDefaultMemberId() : memberId);
    }

    @Transactional
    public void lrentalContract(String orderNo, LRentalContractUpdateRequest request) {
        Orders order = getOrder(orderNo);
        order.setLrentalContractNo(request.getLrentalContract());
        orderRepository.save(order);
    }

    public OrderResponse getUserOrderV2(String orderNo, int acctId) {
        Orders order = getUserOrder(orderNo, acctId);
        Contract contract = order.getContract();
        MainContract mainContract = contract.getMainContract();
        CarResponse carResponse = carsService.getCarInfo(mainContract.getPlateNo());
        if (order.getContract().getMainContract().getAcctId() != acctId) {
            throw new SubscribeException(ORDER_NOT_FOUND);
        }
        CommonOrderPriceInfoResponse priceInfoResponse = null;
        OrderResponse response = new OrderResponse();
        BeanUtils.copyProperties(order, response);
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(order.getOrderNo());
        if (order.getStatus() <= CREDITED.getStatus() && order.getIsNewOrder()) {
            priceInfoResponse = new CommonOrderPriceInfoResponse(mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit(), order.getExpectStartDate());
        } else {
            List<OrderPriceInfo> unPiadorderPriceInfoList = priceInfoWrapper.getUnpaidAvailable().excludeType(Refund).getList();
            if (!unPiadorderPriceInfoList.isEmpty()) {
                int amt = unPiadorderPriceInfoList.stream().mapToInt(PriceInfoInterface::getActualPrice).sum();
                Instant minDate = unPiadorderPriceInfoList.stream().min(Comparator.comparing(OrderPriceInfo::getLastPayDate)).map(OrderPriceInfo::getLastPayDate).orElse(Instant.now());
                priceInfoResponse = new CommonOrderPriceInfoResponse(amt, minDate);
            }
        }

        response.setMileageFee(((MileagePriceInfoWrapper) priceInfoWrapper.getByCategory(MileageFee)).getMileage());
        if (!CollectionUtils.isEmpty(priceInfoWrapper.getByCategory(Insurance).getList())) {
            response.setDisclaimerFee(mainContract.getOriginalPriceInfo().getDisclaimerFee());
        }
        if (!CollectionUtils.isEmpty(priceInfoWrapper.getByCategory(Replacement).getList())) {
            response.setReplacementCarFee(mainContract.getOriginalPriceInfo().getReplacementCarFee());
        }

        response.setIsRenewable(contractLogic.publicIsRenewable(order, acctId));
        response.setPriceInfoResponse(priceInfoResponse);
        response.setCar(carResponse);
        return response;
    }

    /**
     * 取得使用者訂單
     */
    public Orders getUserOrder(String orderNo, int acctId) {
        Orders order = orderRepository.findById(orderNo).orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));
        if (order.getContract().getMainContract().getAcctId() != acctId) {
            throw new SubscribeException(ORDER_NOT_FOUND);
        }
        return order;
    }

    /**
     * 取得訂單
     */
    public Orders getOrder(String orderNo) {
        return orderRepository.findById(orderNo).orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));
    }

    /**
     * 透過 AcctIds 取得訂單
     */
    public List<Orders> getOrdersByAcctIds(List<Integer> acctIds) {
        return orderRepository.getOrdersByAcctIds(acctIds);
    }

    /**
     * 取得收銀台訂單
     */
    public CashierOrderResponse getCashierOrder(String orderNo) {
        PaymentInfo securityPayment =
            paymentService.getPaymentsByOrder(orderNo).stream().filter(paymentInfo -> PayAuth.equals(paymentInfo.getPaymentCategory()) && SecurityDeposit.equals(paymentInfo.getPayFor())).findAny().orElse(null);
        Orders orders = orderRepository.findById(orderNo).orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));
        OrderUtils.sortRemarks(orders);
        CashierOrderResponse response = new CashierOrderResponse();
        BeanUtils.copyProperties(orders, response);
        response.setSecurityDepositPaymentCardNo(Optional.ofNullable(securityPayment).map(PaymentInfo::getCardNumber).orElse(null));
        response.setIsLegalOperationExecuted(LegalOperationReason.isLegalOperationExecuted(orders.getLegalOperationReason()));
        response.setExecutedLegalOperationReason(LegalOperationReason.of(orders.getLegalOperationReason()));
        MainContract mainContract = orders.getContract().getMainContract();
        BuChangeLog buChangeLog = Optional.ofNullable(buChangeService.getLastOrderNoLog(orders.getOrderNo())).orElseGet(BuChangeLog::new);
        buChangeLog.setFirstToBuId(Optional.ofNullable(buChangeService.getToSubscribeFirstLog(mainContract.getMainContractNo(), mainContract.getPlateNo())).map(BuChangeLog::getFromBuId).orElse(null));

        response.setBuChangeLog(buChangeLog);

        setOrderCreatorName(orders, response);

        return response;
    }

    /**
     * 透過主約拿取客戶訂單資訊
     */
    public Page<UserOrderResponse> getUserOrderResponse(PageRequest pageRequest, String mainContractNo, Integer acctId, List<Integer> orderStatus) {
        OrdersCriteria criteria = new OrdersCriteria();
        criteria.setMainContractNo(mainContractNo);
        criteria.setAcctId(Collections.singletonList(acctId));
        criteria.setStatus(orderStatus);
        long total = orderRepository.count(criteria);
        if (0 == total) {
            return Page.of(0, Collections.emptyList(), pageRequest.getLimit(), pageRequest.getSkip());
        }
        Boolean isBlackList = !authServer.checkAcctBlackList(Lists.newArrayList(acctId)).isEmpty();
        List<UserOrderResponse> orderQueryList =
            orderRepository.findBySearch(criteria, pageRequest.getLimit(), pageRequest.getSkip()).stream()
                .map(o -> new UserOrderResponse(o.getOrders(), priceInfoService.generatePriceInfoResponse(o.getOrders()), contractLogic.publicIsRenewable(o.getOrders(), isBlackList))).collect(Collectors.toList());
        Map<String, List<OrderPriceInfo>> orderPriceInfoMap =
            priceInfoService.getPriceInfosByOrders(orderQueryList.stream().map(UserOrderResponse::getOrderNo).collect(Collectors.toList())).stream().collect(Collectors.groupingBy(OrderPriceInfo::getOrderNo));
        orderQueryList.forEach(o -> {
            List<OrderPriceInfo> orderPriceInfoList = orderPriceInfoMap.get(o.getOrderNo());
            if (orderPriceInfoList != null) {
                orderPriceInfoList.stream().filter(opi -> opi.getCategory() == MileageFee && opi.getInfoDetail() != null && opi.getInfoDetail().getMileageFee() != null).findAny().map(OrderPriceInfo::getInfoDetail).map(PriceInfoDetail::getMileageFee)
                    .ifPresent(fee -> o.setMileageFee(o.getMileageFee()));
            }
        });
        return Page.of(total, orderQueryList, pageRequest.getSkip(), pageRequest.getLimit());
    }

    /**
     * 設置訂單建立者名稱
     */
    private void setOrderCreatorName(Orders orders, CashierOrderResponse response) {
        // 首先檢查 OrderPlatform 來決定使用哪種方式獲取 creator name
        if (OrderPlatform.CASHIER == orders.getOrderPlatform() || orders.getOrderPlatform() == null) {
            // OrderPlatform 為 CASHIER 或 null 時，優先嘗試使用 memberId 獲取名稱
            if (orders.getMemberId() != null) {
                response.setOrderCreatorName(authorityServer.getMemberInfos(orders.getMemberId())
                    .stream()
                    .findFirst()
                    .map(MemberInfo::getMemberName)
                    .orElse(null));
                return;
            }

            // 只有在 OrderPlatform 為 null 且 memberId 為 null 的情況下，才嘗試使用 acctId
            if (orders.getOrderPlatform() == null && orders.getAcctId() != null) {
                response.setOrderCreatorName(authServer.getUserWithRetry(orders.getAcctId()).getAcctName());
            }
        } else {
            // 其餘 OrderPlatform 使用 acctId 獲取名稱
            if (orders.getAcctId() != null) {
                response.setOrderCreatorName(authServer.getUserWithRetry(orders.getAcctId()).getAcctName());
            }
        }
    }

    /**
     * 取得訂單
     */
    public List<Orders> getOrders(List<String> orderNos) {
        return orderRepository.findAllById(orderNos);
    }

    /**
     * 取得訂單
     */
    public OrderQueryResponse getQueryOrder(String orderNo) {
        OrdersCriteria criteria = new OrdersCriteria();
        criteria.setOrderNo(orderNo);
        List<OrderQueryResponse> responses = searchPage(criteria, 1, 0);
        return responses.isEmpty() ? null : responses.get(0);
    }

    /**
     * 出車資料確認更新訂單(By Contract)
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public ArrayList<String> departUpdateOrderByContract(String econtractRefEntityNo, CarDepartRequest departRequest, String memberId) {
        EContractReferencable eContractRefEntity = econtractService.fetchEContractRefEntity(econtractRefEntityNo);
        if (!econtractService.isSignEContract(eContractRefEntity)) {
            throw new SubscribeException(FORMAL_ECONTRACT_NOT_FOUND);
        }
        if (ContractUtils.isContract(econtractRefEntityNo)) {
            Orders orders = getBookingOrder((Contract) eContractRefEntity);
            return departUpdateOrder(orders.getOrderNo(), departRequest, memberId);
        } else {
            DealerOrder dealerOrder = (DealerOrder) eContractRefEntity;
            if (!dealerOrder.getOrderStatus().equals(ContractStatus.CREATE.getCode())) {
                throw new SubscribeException(DEALER_ORDER_STATUS_NOT_ORDERING);
            }
            AuthDealerUserResponse dealerUser = authServer.getDealerUser(dealerOrder.getDealerUserId());
            dealerOrderService.updateDealerOrder(new DealerOrderUpdateRequest(dealerOrder, dealerUser, departRequest));
            return new ArrayList<>();
        }
    }

    private Orders getBookingOrder(Contract contract) {
        return contract.getOrders().stream().filter(order -> BOOKING.getStatus() == order.getStatus()).findAny().orElseThrow(() -> new SubscribeException(ORDERING_ORDER_NOT_FOUND));
    }

    /**
     * 出車資料確認更新訂單
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public ArrayList<String> departUpdateOrder(String orderNo, CarDepartRequest departRequest, String memberId) {
        // 原訂單資料 from DB
        Orders order = this.getOrder(orderNo);
        Contract contract = order.getContract();
        MainContract mainContract = order.getContract().getMainContract();
        OrderStatus orderStatus = OrderStatus.of(order.getStatus());
        String oriPlateNo = mainContract.getPlateNo();

        // 蒐集錯誤訊息
        ArrayList<String> errorMessage = new ArrayList<>();


        // 檢查是否為訂閱車
        String usePlateNo = departRequest.getPlateNo();

        // 檢查車籍
        Cars useCar = Optional.ofNullable(carsService.findByPlateNo(usePlateNo)).orElseThrow(() -> new SubscribeException(CAR_NOT_FOUND));
        Cars oriCar = carsService.findByPlateNo(oriPlateNo);

        if (CarsUtil.isCarPlusCar(useCar.getVatNo())) {
            List<String> subscribeResult = crsService.checkIsSubscribeCars(usePlateNo);
            if (!subscribeResult.isEmpty()) {
                errorMessage.addAll(subscribeResult);
            }
        }

        if (order.getIsNewOrder() && departRequest.getDepartDate().after(new Date())) {
            errorMessage.add(DEPART_TIME_SHOULD_LESS_THAN_NOW.getMsg());
        }


        // 確認狀態
        if (orderStatus != BOOKING) {
            errorMessage.add(DEPART_ORDER_STATUS_CONSTRAINT.getMsg());
        }

        // 確認前約是否還車
        if (!order.getIsNewOrder()) {
            Orders preOrder = orderRepository.getPreviousOrders(order.getOrderNo());
            if (preOrder.getStatus() <= DEPART.getStatus()) {
                errorMessage.add(PRE_ORDER_NOT_RETURN.getMsg());
            }
        }

        if (order.getIsNewOrder()) {
            // 檢查車籍
            if (useCar.getLaunched() == CarDefine.Launched.deprecate) {
                errorMessage.add(CAR_LAUNCHED_DEPRECATE.getMsg());
            }
            if (useCar.getLaunched() == CarDefine.Launched.accident) {
                errorMessage.add(CAR_LAUNCHED_ACCIDENT.getMsg());
            }
        }

        if (!Objects.equals(oriPlateNo, usePlateNo)) {
            // 比較是否啟用優惠月費
            if (useCar.isMonthlyDiscounted() != mainContract.getOriginalPriceInfo().isMonthlyDiscounted()) {
                errorMessage.add(CAR_IS_MONTHLY_DISCOUNTED_NOT_MATCH_WITH_MAIN_CONTRACT.getMsg());
            }

            SubscribeLevel carSubscribeLevel = subscribeLevelService.findByLevel(useCar.getSubscribeLevel());

            boolean isDiscountLevelEnabled = subscribeLevelService.isDiscountLevelEnabled(useCar, carSubscribeLevel);

            if (!isSubscribeLevelMatch(useCar, mainContract)) {
                if (!isDiscountLevelEnabled || !isDiscountLevelMatch(mainContract, carSubscribeLevel)) {
                    if (!isDiscountLevelEnabled && isDiscountLevelMatch(mainContract, carSubscribeLevel)) {
                        errorMessage.add(CAR_DISCOUNT_LEVEL_NOT_MATCH_WITH_MAIN_CONTRACT.getMsg());
                    } else {
                        errorMessage.add(CAR_SUBSCRIBE_LEVEL_NOT_MATCH_WITH_MAIN_CONTRACT.getMsg());
                    }
                }
            } else {
                if (mainContract.getOriginalPriceInfo().isLevelDiscounted()) {
                    errorMessage.add(CAR_DISCOUNT_LEVEL_NOT_MATCH_WITH_MAIN_CONTRACT.getMsg());
                }
            }
        }

        // 檢查里程
        if (departRequest.getDepartMileage() < useCar.getCurrentMileage()) {
            errorMessage.add(DEPART_MILEAGE_SHOULD_MORE_THAN_CURRENT_MILEAGE.getMsg());
        }

        // 檢查保證金是否已付款
        if (mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getUnpaidSecurityDeposit() != 0) {
            errorMessage.add(ORDER_PRICE_INFO_SECURITY_DEPOSIT_NON_PAYMENT.getMsg());
        }

        // 若有錯誤訊息
        if (!errorMessage.isEmpty()) {
            return errorMessage;
        }

        // 更新車色、etag 型式、etag 照片 url
        if (StringUtils.isNotBlank(departRequest.getColor())) {
            useCar.setColorDesc(departRequest.getColor());
        }
        Optional.ofNullable(departRequest.getEtagModel()).ifPresent(useCar::setEtagModel);
        Optional.ofNullable(departRequest.getEtagNo()).ifPresent(useCar::setEtagNo);

        // 新車從空車00狀態變更為鎖車20狀態
        changePlateNo(order, mainContract, useCar, oriCar, memberId);
        // 更新訂單
        order.setStartDate(departRequest.getDepartDate().toInstant());

        // 設定第一期里程費用的出車里程
        OrderPriceInfo mileageOrderPriceInfo = priceInfoService.getPriceInfoWrapper(orderNo).getByCategoryType(MileageFee, Pay).getList().stream().filter(orderPriceInfo -> orderPriceInfo.getStage() == 1)
            .findFirst().orElseThrow(() -> new SubscribeException(
                ORDER_PRICE_INFO_FIRST_STAGE_MILEAGE_NOT_FOUND));
        mileageOrderPriceInfo.getInfoDetail().setStartMileage(departRequest.getDepartMileage());
        orderPriceInfoRepository.save(mileageOrderPriceInfo);
        order.setDepartMileage(departRequest.getDepartMileage());
        // 出車備註改儲存至訂單備註
        appendRemark(order, departRequest.getDepartRemark(), memberId);

        // 更新合約，主合約出車時間
        if (contract.getStage() == 1) {
            mainContract.setStartDate(order.getStartDate());
            if (order.getStage() == 1) {
                contract.setStartDate(order.getStartDate());
            }
        }
        econtractService.updateEContractBeforeSign(order, useCar.getCarNo());
        contractService.updateContract(contract);
        contractService.updateMainContract(mainContract);

        return errorMessage;
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void changePlateNo(Orders order, MainContract mainContract, Cars useCar, Cars oriCar, String memberId) {
        carsService.updateCrsCarNo(useCar);
        carsService.updateCrsCarNo(oriCar);
        boolean isCarPlusCar = CarsUtil.isCarPlusCar(useCar.getVatNo());
        // 專案車合約結束日檢查
        if (useCar.getCrsCarNo() != null && isCarPlusCar) {
            PurchaseProjectCarSearchResponse purchaseProjectCarSearchResponse = crsService.searchProjectCar(useCar.getCrsCarNo());
            if (purchaseProjectCarSearchResponse != null && Objects.equals(purchaseProjectCarSearchResponse.getIsProjectCar(), Boolean.TRUE)) {
                purchaseProjectCarSearchResponse.validate(order.getExpectEndDate());
            }
        }
        if (StringUtils.isNotBlank(useCar.getPlateNo()) && !Objects.equals(oriCar.getPlateNo(), useCar.getPlateNo())) {
            if (order.getIsNewOrder() && !CarDefine.CarStatus.Free.getCode().equals(useCar.getCarStatus())) {
                throw new SubscribeException(CAR_NOT_FREE);
            }
            if (isEContractSigned(order.getContractNo())) {
                throw new SubscribeException(CANNOT_CHANGE_PLATE_NO_AFTER_ECONTRACT_SIGNED);
            }
            // 當車號異動時，將原車號進行下架(因車可能有異味或車況有問題)，再由營業判定是否上架
            if (!oriCar.isVirtualCar() && oriCar.getCrsCarNo() != null) {
                carsService.launchClose(order.getPlateNo());
                // 撥車處理
                buChange(order, useCar, oriCar, memberId);
            }
            // 原車從鎖車20狀態變更為空車00狀態
            carsService.updateStatus(oriCar, CarDefine.CarStatus.Free);
            // 新車從空車00狀態變更為鎖車20狀態
            carsService.updateStatus(useCar, CarDefine.CarStatus.Subscribed);
            mainContract.setPlateNo(useCar.getPlateNo());
        }
    }

    /**
     * 車籍異動，撥車處理
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void buChange(Orders order, Cars useCar, Cars oriCar, String memberId) {
        carsService.updateCrsCarNo(oriCar);
        carsService.updateCrsCarNo(useCar);
        if (oriCar.getCrsCarNo() != null && oriCar.getCrsCarNo().equals(useCar.getCrsCarNo())) {
            carsService.changeSameCar(oriCar, useCar, order.getOrderNo());
            if (StringUtils.isNotBlank(order.getLrentalContractNo())) {
                notifyService.notifyContractChange(order);
            }
        } else {
            // 通知營業取消長租合約
            notifyService.notifyContractCancel(order, oriCar.getPlateNo(), useCar.getPlateNo(), order.getLrentalContractNo());
            // 若為自動撥車，則發動自動還車
            if (oriCar.getBuChangeMasterId() != null) {
                buChangeService.changeReturn(order, crsService.getCar(oriCar.getPlateNo()), memberId, oriCar);
                carsService.update(oriCar);
            } else {
                order = generateCenterContract(order, oriCar, true);
            }
            crsService.subscribeCarControl(useCar);
            crsService.unsubscribeCarControl(oriCar);
            order.setLrentalContractNo(null);
            orderRepository.save(order);
        }
    }

    /**
     * 產生出車任務
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void generateDepartTask(String orderNo, DepartTaskRequest departTaskRequest) {

        Orders order = this.getOrder(orderNo);
        Contract contract = order.getContract();

        RentalFormType rentalFormType = departTaskRequest.getRentalFormType();
        contract.setRentalFormType(rentalFormType);

        if (RentalFormType.ELECTRONIC.equals(rentalFormType)) {
            if (StringUtils.isNotBlank(contract.getDepartTaskId())) {
                rentalTaskService.resetTask(Integer.valueOf(contract.getDepartTaskId()));
            }

            // 出車任務前置條件
            if (StringUtils.isBlank(contract.getDepartTaskId())
                && DateUtils.isSameDay(Date.from(contract.getExpectStartDate()), Date.from(order.getExpectStartDate()))
                && BOOKING.getStatus() == order.getStatus()) {
                // 產生出車任務
                rentalTaskService.generateTask(order, TaskType.DEPART);
            }
        } else if (RentalFormType.PAPER.equals(rentalFormType) && StringUtils.isNotBlank(contract.getDepartTaskId())) {
            rentalTaskService.deleteTask(order, TaskType.DEPART);
            contract.setDepartTaskId(null);
        }

        contractService.updateContract(contract);

        // 若選擇紙本出租單模式，則執行出車邏輯
        if (RentalFormType.PAPER.equals(rentalFormType)) {
            CarDepartFlowRequest carDepartFlowRequest = new CarDepartFlowRequest();
            carDepartFlowRequest.setDepartMemberId(departTaskRequest.getDepartMemberId());
            departCar(order, carDepartFlowRequest);
        }
    }

    /**
     * 出車流程 (By Contract)
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void departCarByContract(String contractNo, CarDepartFlowRequest departRequest) {
        Contract contract = contractService.getContractAndOrdersByContractNo(contractNo);
        Orders orders = contract.getOrders().stream().filter(order -> BOOKING.getStatus() == order.getStatus()).findAny().orElseThrow(() -> new SubscribeException(ORDERING_ORDER_NOT_FOUND));
        departCar(orders.getOrderNo(), departRequest);
    }


    /**
     * 出車流程
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void departCar(String orderNo, CarDepartFlowRequest request) {
        Orders order = this.getOrder(orderNo);
        departCar(order, request);
    }

    /**
     * 出車流程
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void departCar(Orders order, CarDepartFlowRequest request) {

        Contract contract = order.getContract();
        String departMemberId = request.getDepartMemberId();

        departCarProcess(contract, departMemberId);
    }

    /**
     * 出車中換車 <br/>
     * 流程 :  <br/>
     * Step.1 資料查詢 <br/>
     * {@link CarsService#findByPlateNo(String)} <br/>
     * {@link CrsService#getCar(String)} <br/>
     * {@link ContractService#getContractByContractNo(String)} <br/>
     * {@link ContractService#getMainContractByNo(String)} <br/>
     * {@link SubscribeLevelService#findByLevel(int)} <br/>
     * {@link AuthorityServer#getMemberInfos(String)} <br/>
     * Step.2 條件檢核 <br/>
     * {@link CarsUtil#replaceCarAudit(MainContract, Orders, List, String, Cars, Integer, CarBaseInfoSearchResponse, Cars, Integer)}    <br/>
     * Step.3 Call Internal Services P1<br/>
     * {@link OrderService#replaceCarInternalServP1(Orders, MemberInfo, Cars, Cars, MainContract, Date, String, Integer, Integer)} <br/>
     * Step.4 Call External Services P1 <br/>
     * {@link OrderService#replaceCarExternalServP1(Orders, Cars, Integer)} <br/>
     * Step.5 Call Internal Services P2 <br/>
     * {@link OrderService#replaceCarInternalServP2(Contract, Integer)} <br/>
     * Step.6 Call External Services P2 <br/>
     * {@link OrderService#replaceCarExternalServP2(Orders, Cars, CarBaseInfoSearchResponse, Cars, CarBaseInfoSearchResponse, MemberInfo, String, List, String)} <br/>
     */
    public String replaceCar(String orderNo, CarReplaceRequest carReplaceRequest, String companyId, String memberId, String outCarPlateNo) {

        String inCarPlateNo = carReplaceRequest.getInCarPlateNo();
        Integer inCarStartMileage = carReplaceRequest.getInCarStartMileage();
        Integer outCarEndMileage = carReplaceRequest.getOutCarEndMileage();
        String replacedCarMemo = carReplaceRequest.getReplaceMemo();
        Date replaceDate = carReplaceRequest.getReplaceDate();
        boolean isCarPlusCar = CarsUtil.isCarPlusCar(outCarPlateNo);
        List<String> lrContractReplaceCode = carReplaceRequest.getLrContractReplaceCode();
        String lrContractMemo = carReplaceRequest.getLrContractMemo();

        // 取得汰換車籍資料
        Cars outCar = Optional.ofNullable(carsService.findByPlateNo(outCarPlateNo)).orElseThrow(() -> new BadRequestException(String.format("查無汰換車車號 %s", outCarPlateNo)));
        // 取得替代車籍資料
        Cars inCar = Optional.ofNullable(carsService.findByPlateNo(inCarPlateNo)).orElseThrow(() -> new BadRequestException(String.format("查無替代車車號 %s", inCarPlateNo)));
        // 取得汰換車、替代車CRS資料
        CarBaseInfoSearchResponse outCarCrsInfo = crsService.getCar(outCarPlateNo);
        CarBaseInfoSearchResponse inCarCrsInfo = crsService.getCar(inCarPlateNo);
        if (inCarCrsInfo == null && isCarPlusCar) {
            throw new SubscribeException(CRS_CAR_NOT_FOUND);
        }

        // 取得該訂單資料
        Orders order = getOrder(orderNo);
        // 取得合約
        Contract contract = contractService.getContractByContractNo(order.getContractNo());
        // 取得主約
        MainContract mainContract = contractService.getMainContractByNo(contract.getMainContractNo());
        SubscribeLevel adoptedSubscribeLevel = subscribeLevelService.determineSubscribeLevel(inCar);
        if (!Objects.equals(adoptedSubscribeLevel.getLevel(), mainContract.getGivenCarLevel())) {
            throw new SubscribeException(CAR_SUBSCRIBE_LEVEL_NOT_MATCH_WITH_MAIN_CONTRACT);
        }
        // 換車員工
        MemberInfo member = authorityServer.getMemberInfos(memberId).get(0);

        List<String> messages = replaceCarAudit(mainContract, order, lrContractReplaceCode, lrContractMemo,
            inCar, inCarStartMileage, inCarCrsInfo, outCar, outCarEndMileage);

        String resultMsg = String.join(", ", messages);
        if (!resultMsg.isEmpty()) {
            return resultMsg;
        }

        self.replaceCarInternalServP1(order, member, outCar, inCar, mainContract, replaceDate, replacedCarMemo,
            outCarEndMileage, inCarStartMileage);

        Integer departTaskId = self.replaceCarExternalServP1(order, outCar, outCarEndMileage);

        self.replaceCarInternalServP2(contract, departTaskId);

        if (CarsUtil.isCarPlusCar(outCar.getVatNo()) && CarsUtil.isCarPlusCar(inCar.getVatNo())) {
            self.replaceCarExternalServP2(order, outCar, outCarCrsInfo,
                inCar, inCarCrsInfo, member, companyId, lrContractReplaceCode, lrContractMemo);
        }
        buChangeService.changeReturn(order, outCarCrsInfo, memberId, outCar);
        String replaceDateStr = DateUtil.getFormatString(replaceDate.toInstant(), SLASH_FORMATTER_WITHOUT_TIME);
        notifyService.notifyReplaceCarSuccess(order, outCar, inCar, replaceDateStr, replacedCarMemo);
        return resultMsg;
    }

    /**
     * Step.1 汰換車遠通還車資料異動 : 計算還車 Etag 費用 <br/>
     * Step.2 汰換車遠通還車確認 <br/>
     * Step.3 替代車遠通出車 : 建立 Etag Info <br/>
     * Step.4 更新主約 汰換車PlateNo 為 替代車PlateNo <br/>
     * Step.5 新增訂單 remarks 【換車】{{換車日}}:{{汰換車plateNo}} 異動為 {{替代車plateNo}} (原因:{{換車原因}})) <br/>
     * Step.6 計算汰換車里程費用(order_price_info - Others) <br/>
     * Step.7 更新訂單 departMileage 為替代車的出發里程數(以後台Key-in(inCarStartMileage)為主) <br/>
     * Step.8 更新車籍 cars <br/>
     * - 替代車 status = 30, BookingOrderNo = Current OrderNo  <br/>
     * - 汰換車 status = 00, BookingOrderNo = null, CurrentMileage = 還車里程數 <br/>
     */
    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRED)
    public void replaceCarInternalServP1(Orders order, MemberInfo member, Cars outCar, Cars inCar,
                                         MainContract mainContract, Date replaceDate, String replacedCarMemo,
                                         Integer outCarEndMileage, Integer inCarStartMileage) {
        log.info("replaceCar InternalServ P1");
        String memberId = member.getMemberId();
        String memberName = member.getMemberName();
        eTagService.returnCarModifyForReplace(order, outCar.getPlateNo(), memberId, mainContract.getReturnStationCode());
        eTagService.closeOutCarCheck(order);
        eTagService.rentCar(order, inCar.getPlateNo(), memberId);

        order.getContract().getMainContract().setPlateNo(inCar.getPlateNo());
        mainContract.setPlateNo(inCar.getPlateNo());
        contractService.updateMainContract(mainContract);

        String replaceDateString = DateUtils.toDateString(replaceDate, "yyyy/MM/dd");
        Remark remark = buildRemark(String.format("【換車】%s:%s 異動為 %s (原因:%s)", replaceDateString, outCar.getPlateNo(), inCar.getPlateNo(), replacedCarMemo), memberId, memberName);
        appendRemark(order, remark);

        saveReplaceMileageFee(outCar.getPlateNo(), order, order.getDepartMileage(), outCarEndMileage, inCarStartMileage);

        order.setDepartMileage(inCarStartMileage);
        updateOrder(order);

        String existBookingOrderNo = outCar.getBookingOrderNo();
        inCar.setBookingOrderNo(existBookingOrderNo);
        inCar.setCarStatus(CarDefine.CarStatus.BizOut.getCode());
        carsService.update(inCar);
        outCar.setBookingOrderNo(null);
        outCar.setCurrentMileage(outCarEndMileage);
        outCar.setCarStatus(CarDefine.CarStatus.Free.getCode());
        carsService.update(outCar);
        csatService.updatePlateNoCauseReplaceCar(order);
    }

    /**
     * 費用類型 : Others <br/>
     * 出車中換車 - 汰換車里程數費用
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void saveReplaceMileageFee(String outCarPlateNo, Orders order, Integer orderDepartMileage,
                                      Integer outCarEndMileage, Integer inCarStartMileage) {
        CalculateStage currentStage = DateUtil.calculateStageAndDateByTargetDate(order, Instant.now());
        List<OrderPriceInfo> opis = priceInfoService.getPriceInfoWrapper(order.getOrderNo()).getByCategoryType(MileageFee, Pay).getList();
        for (OrderPriceInfo opi : opis) {
            if (opi.getStage().equals(currentStage.getStage())) {
                double outCarMileageFee = Optional.ofNullable(opi.getInfoDetail()).map(PriceInfoDetail::getMileageFee).orElse(order.getContract().getMainContract().getOriginalPriceInfo().getMileageFee());
                opi.getInfoDetail().setStartMileage(inCarStartMileage);
                orderPriceInfoRepository.save(opi);
                OrderPriceInfo replacedMileageFee = new OrderPriceInfo();
                replacedMileageFee.setOrderNo(order.getOrderNo());
                replacedMileageFee.setStage(opi.getStage());
                replacedMileageFee.setReceivableDate(convertToStartOfInstant(Instant.now()));
                replacedMileageFee.setCategory(Others);
                replacedMileageFee.setType(Pay.getCode());
                replacedMileageFee.setAmount(calculateCarMileageFee(outCarMileageFee, orderDepartMileage, outCarEndMileage));
                PriceInfoDetail pid = new PriceInfoDetail();
                pid.setStartMileage(orderDepartMileage);
                pid.setEndMileage(outCarEndMileage);
                pid.setTotalMileage(outCarEndMileage - orderDepartMileage);
                String reason = String.format("%s里程費結算(%dkm-%dkm)", outCarPlateNo, orderDepartMileage, outCarEndMileage);
                pid.setReason(reason);
                replacedMileageFee.setInfoDetail(pid);
                replacedMileageFee.setLastPayDate(getLastPayDate(order));
                orderPriceInfoRepository.save(replacedMileageFee);
            }
        }
    }

    /**
     * Step.1 更新汰換車 CRS 里程數 (updateKm) <br/>
     * Step.2-1 訂單 A-1-1 清空出車任務、產生出車任務Id <br/>
     */
    public Integer replaceCarExternalServP1(Orders order, Cars outCar, Integer outCarEndMileage) {
        log.info("replaceCar ExternalServ P1");
        crsService.updateKm(outCar, outCarEndMileage, order.getOrderNo());
        return self.delOldAndGenNewContractDepartTaskId(order);
    }

    /**
     * 1. 刪除合約原出車任務 <br/>
     * 2. 取得新出車任務Id <br/>
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Integer delOldAndGenNewContractDepartTaskId(Orders order) {
        rentalTaskService.deleteTask(order, TaskType.DEPART);
        return rentalTaskService.generateTask(order, TaskType.DEPART);
    }

    /**
     * Step.2-2 更新合約 出車任務Id <br/>
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void replaceCarInternalServP2(Contract contract, Integer departTaskId) {
        log.info("replaceCar InternalServ P2");
        contract.setDepartTaskId(String.valueOf(departTaskId));
        contractService.updateContract(contract);
    }

    /**
     * Step.3 LR建立契約 <br/>
     * ------a   if 汰換車 bu=4, then 建立汰換車訂閱部門約 (起租日=換車日隔天), ELSE 不建立契約 <br/>
     * ------b.1 if 替代車 bu=4, then 建立替代車訂閱客戶約 (起租日=換車日當天), ELSE 不建立契約 <br/>
     * ------b.2 更新訂單契約編號 (替代車訂閱客戶約編號) <br/>
     * ------b.3 CI 通知投保 <br/>
     * Step.3-cond-1 [buId=1]換[buId=4]: 只需建立訂閱客戶約(替代車)(Order.LrentalContractNo = 新的訂閱客戶約No) <br/>
     * Step.3-cond-2 [buId=4]換[buId=1]: 只需要建立訂閱部門約(汰換車)(Order.LrentalContractNo = null) <br/>
     * Step.3-cond-3 [buId=4]換[buId=4]: 皆會建立訂閱部門約(汰換車)、建立訂閱客戶約(替代車)(Order.LrentalContractNo = 新的訂閱客戶約No) <br/>
     */
    public void replaceCarExternalServP2(Orders order, Cars outCar, CarBaseInfoSearchResponse outCarCrsInfo,
                                         Cars inCar, CarBaseInfoSearchResponse inCarCrsInfo, MemberInfo member, String companyId,
                                         List<String> lrContractReplaceCodes, String lrContractMemo) {
        log.info("replaceCar ExternalServ P2");
        boolean isOutCarSubBuId = Objects.equals(BuIdEnum.subscribe.getCode(), outCarCrsInfo.getBuId());
        boolean isInCarSubBuId = Objects.equals(BuIdEnum.subscribe.getCode(), inCarCrsInfo.getBuId());

        if (!isOutCarSubBuId && isInCarSubBuId) {
            log.info("[buId=1]換[buId=4], Out Car {} -> In Car {}, 建立訂閱客戶約(Order.LrentalContractNo = 新的訂閱客戶約No)", outCarCrsInfo.getPlateNo(), inCarCrsInfo.getPlateNo());
            createSubClientContract(order, member, companyId, inCar, inCarCrsInfo, lrContractReplaceCodes, lrContractMemo);
        }

        if (isOutCarSubBuId) {
            log.info("[buId=4]換[buId=1], Out Car {} -> In Car {}, 建立訂閱部門約(Order.LrentalContractNo = null)", outCarCrsInfo.getPlateNo(), inCarCrsInfo.getPlateNo());
            createSubDeptContract(order, outCar);
        }

        if (isOutCarSubBuId && isInCarSubBuId) {
            log.info("[buId=4]換[buId=4], Out Car {} -> In Car {}, 建立訂閱客戶約(Order.LrentalContractNo = 新的訂閱客戶約No)", outCarCrsInfo.getPlateNo(), inCarCrsInfo.getPlateNo());
            createSubClientContract(order, member, companyId, inCar, inCarCrsInfo, lrContractReplaceCodes, lrContractMemo);
        }
    }

    private void createSubDeptContract(Orders order, Cars outCar) {
        generateCenterContract(order, outCar, false);
        order.setLrentalContractNo(null);
        self.updateOrder(order);
    }

    private void createSubClientContract(Orders order,
                                         MemberInfo member, String companyId,
                                         Cars inCar, CarBaseInfoSearchResponse inCarCrsInfo,
                                         List<String> lrContractReplaceCodes, String lrContractMemo) {
        Orders orders = self.createLrentalContract(order, member.getMemberId(), inCar, inCarCrsInfo,
            lrContractReplaceCodes, lrContractMemo, null);
        InsuranceContractRequest insuranceContractRequest = new InsuranceContractRequest();
        insuranceContractRequest.setLrentalContractNo(orders.getLrentalContractNo());
        insuranceContractRequest.setOrderNo(order.getOrderNo());
        createBatchInsurance(insuranceContractRequest, member.getMemberId(), companyId);
    }

    /**
     * 出車資料驗證
     */
    private void departCarValidate(Orders order, MainContract mainContract, Cars car, String departMemberId) {
        // 檢查訂單
        if (!order.getStatus().equals(BOOKING.getStatus())) {
            throw new SubscribeException(DEPART_ORDER_STATUS_NOT_BOOKING);
        }
        if (Objects.isNull(order.getStartDate())) {
            throw new SubscribeException(EXPECT_DEPART_DATE_NOT_FOUND);
        }
        if (Objects.isNull(order.getDepartMileage())) {
            throw new SubscribeException(DEPART_ORDER_DEPARTMILEAGE_NOT_FOUND);
        }

        // 確認前約是否還車
        if (!order.getIsNewOrder()) {
            Orders preOrder = orderRepository.getPreviousOrders(order.getOrderNo());
            if (preOrder.getStatus() <= DEPART.getStatus()) {
                throw new SubscribeException(PRE_ORDER_NOT_RETURN);
            }
        }
        // 出車檢查是否有車籍契約
        if (CarsUtil.isCarPlusCar(car.getVatNo()) && StringUtils.isBlank(order.getLrentalContractNo())
            && !Objects.equals(Optional.ofNullable(crsService.getCar(mainContract.getPlateNo())).map(CarBaseInfoSearchResponse::getBuId).orElse(null), BuIdEnum.lRental.getCode())) {
            throw new SubscribeException(LRENTAL_CONTRACT_NOT_FOUND_CAN_NOT_DEPART);
        }

        // 檢查出車人員
        validateMemberExists(departMemberId);

        // 檢查 所有須繳費用皆已繳納
        OrderPriceInfoCriteria criteria = new OrderPriceInfoCriteria();
        criteria.setOrderNo(Collections.singletonList(order.getOrderNo()));
        criteria.setStage(1);
        criteria.setType(Pay.getCode());
        List<OrderPriceInfo> list = orderPriceInfoRepository.getPriceInfos(criteria);
        for (OrderPriceInfo info : list) {
            if ((info.getReceivedAmount() == null || info.getReceivedAmount() == 0) && info.getAmount() > 0) {
                switch (info.getCategory()) {
                    case SecurityDeposit:
                        throw new SubscribeException(ORDER_PRICE_INFO_SECURITY_DEPOSIT_NON_PAYMENT);
                    case MonthlyFee:
                        throw new SubscribeException(ORDER_PRICE_INFO_MONTHLY_FEE_NON_PAYMENT);
                    case Insurance:
                        throw new SubscribeException(ORDER_PRICE_INFO_INSURANCE_NON_PAYMENT);
                    case Dispatch:
                        throw new SubscribeException(ORDER_PRICE_INFO_DISPATCH_NON_PAYMENT);
                    default:
                }
            }
        }
        // 收支不平衡
        if (!paymentService.checkBalance(order.getOrderNo())) {
            log.error("收支不平衡");
            throw new SubscribeException(ORDER_PRICE_INFO_AND_AMOUNT_NOT_BALANCE);
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void departCarProcess(String econtractRefEntityNo, String departMemberId) {
        try {
            if (ContractUtils.isContract(econtractRefEntityNo)) {
                Contract contract = contractService.getContractAndOrdersByContractNo(econtractRefEntityNo);
                Orders order = departCarProcess(contract, departMemberId);
                authServer.privacyPolicyAccept(departMemberId, order);
            } else {
                EContractReferencable eContractRefEntity = econtractService.fetchEContractRefEntity(econtractRefEntityNo);
                DealerOrder dealerOrder = (DealerOrder) eContractRefEntity;
                DealerOrderDepartRequest dealerOrderDepartRequest = new DealerOrderDepartRequest(dealerOrder);
                dealerOrderService.departDealerOrder(dealerOrderDepartRequest, departMemberId, CsatOrderSource.SEALAND.name());
                authServer.privacyPolicyAccept(departMemberId, dealerOrder);
            }
        } catch (SubscribeException e) {
            if (e.getCode().getCode() == ORDER_PRICE_INFO_SECURITY_DEPOSIT_NON_PAYMENT.getCode()
                || e.getCode().getCode() == ORDER_PRICE_INFO_MONTHLY_FEE_NON_PAYMENT.getCode()
                || e.getCode().getCode() == ORDER_PRICE_INFO_INSURANCE_NON_PAYMENT.getCode()
                || e.getCode().getCode() == ORDER_PRICE_INFO_DISPATCH_NON_PAYMENT.getCode()
                || e.getCode().getCode() == ORDER_PRICE_INFO_AND_AMOUNT_NOT_BALANCE.getCode()
                || e.getCode().getCode() == CHECK_OUT_SECURITY_DEPOSIT_FAIL.getCode()
                || e.getCode().getCode() == CHECK_OUT_FAIL.getCode()
                || e.getCode().getCode() == CHECK_OUT_CANCEL_ORDER_FAIL.getCode()
                || e.getCode().getCode() == CHECK_OUT_ETAG_MULTIPLE_REMIT_FAIL.getCode()
                || e.getCode().getCode() == PAYMENT_INFO_NOT_FOUND.getCode()
                || e.getCode().getCode() == ADVANCE_CHECK_OUT_FAIL.getCode()
                || e.getCode().getCode() == PRICE_AND_INVOICE_AMOUNT_NOT_EQUAL.getCode()) {
                log.error("任務出車失敗:{}", e.getMessage(), e);
                throw new SubscribeException(ORDER_CAN_NOT_DEPART_FOR_TASK);
            } else {
                throw e;
            }
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders departCarProcess(Contract contract, String departMemberId) {

        Orders order = getBookingOrder(contract);
        MainContract mainContract = order.getContract().getMainContract();
        String orderNo = order.getOrderNo();

        String plateNo = mainContract.getPlateNo();
        Cars car = carsService.findByPlateNo(plateNo);

        departCarValidate(order, mainContract, car, departMemberId);

        // 寫入Cars的里程
        car.setCurrentMileage(order.getDepartMileage());
        if (!car.isVirtualCar()) {
            car.setCarStatus(CarDefine.CarStatus.BizOut.getCode());
        }
        carsService.update(car);

        // 計算差異天數
        long diffDays = DateUtil.calculateDiffDate(convertToStartOfInstant(order.getExpectStartDate()),
            convertToStartOfInstant(order.getStartDate()), DAYS);
        // 更新繳費時間
        priceInfoService.updateOrderPriceLastPayDay(order, (int) diffDays);

        // 更新預期還車時間
        ZonedDateTime originalExpectEndDate = order.getExpectEndDate().atZone(DateUtils.ZONE_TPE);
        Instant newExpectEndDate = DateUtil.calculateNewEndDate(Optional.ofNullable(contract.getStartDate()).orElseGet(contract::getExpectStartDate), 12)
            .with(HOUR_OF_DAY, originalExpectEndDate.get(HOUR_OF_DAY))
            .with(MINUTE_OF_HOUR, originalExpectEndDate.get(MINUTE_OF_HOUR)).toInstant();
        Instant newOrderExpectEndDate = DateUtil.calculateNewEndDate(Optional.ofNullable(order.getStartDate()).orElseGet(order::getExpectStartDate), order.getMonth())
            .with(HOUR_OF_DAY, originalExpectEndDate.get(HOUR_OF_DAY))
            .with(MINUTE_OF_HOUR, originalExpectEndDate.get(MINUTE_OF_HOUR)).toInstant();
        contract.setExpectEndDate(newExpectEndDate);
        mainContract.setExpectEndDate(contract.getExpectEndDate());
        order.setExpectEndDate(newOrderExpectEndDate);

        // 更新訂單
        order.setStatus(DEPART.getStatus());
        order.setDepartMemberId(departMemberId);
        orderRepository.save(order);

        // 更新合約
        contract.setStatus(ContractStatus.GOING.getCode());
        if (contract.getStartDate() == null) {
            contract.setStartDate(order.getStartDate());
        }
        contractService.updateContract(contract);

        // 更新主合約
        if (mainContract.getStartDate() == null) {
            mainContract.setStartDate(order.getStartDate());
        }
        mainContract.setStatus(ContractStatus.GOING.getCode());
        contractService.updateMainContract(mainContract);

        // 若非續約訂單，檢查長租契約日期是否與訂單起迄日期一致，不一致則重新建立長租契約
        handleLrentalContractRecreation(order);

        // 通知 Etag 出車
        eTagService.rentCar(order, plateNo, departMemberId);
        checkoutService.checkOut(order);
        if (order.getIsNewOrder() && !Objects.equals(mainContract.getPlateNo(), mainContract.getOrderPlateNo())) {
            paymentService.changeAccountSecurityDepositCar(orderNo, plateNo);
        }

        // 更新會員證件審核狀態
        int acctId = mainContract.getAcctId();
        authorityServer.updateUserApproval(acctId);

        // 所屬公司統編為格上才產生電訪任務
        if (CarsUtil.isCarPlusCar(car.getVatNo())) {
            csatService.createCsat(order);
        }

        // 向 CRS 更新車輛最新里程數
        crsService.updateKm(car, car.getCurrentMileage(), orderNo);
        carsService.updateLocationStationBasedOnSgType(mainContract, car);

        // 電子 [出租單] && [合約] 邏輯合併一起寄送
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 交易提交後執行
                econtractService.asyncGenRentalTaskPdfSendECFilesMail(order, contract, mainContract, departMemberId, TaskType.DEPART);
            }
        });

        checkoutService.modifyMonthlyItem(order);
        authServer.approveAuditPreSignedReview(mainContract.getAcctId(), departMemberId);
        return order;
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void handleLrentalContractRecreation(Orders order) {
        lrentalContractService.prepareLrentalContractRecreationRequest(order)
            .ifPresent(request -> createLrentalContract(order, request, configService.getSubscribeConfig().getSubscribeDefaultMemberId())); // 固定以 訂閱管理課課長 工號建立長租契約
    }

    /**
     * 還車資料確認(By Contract)
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void dropOffCarConfirmByContract(String econtractRefEntityNo, CarDropOffRequest request, String adminId) {
        EContractReferencable eContractRefEntity = econtractService.fetchEContractRefEntity(econtractRefEntityNo);
        if (ContractUtils.isContract(econtractRefEntityNo)) {
            Contract contract = contractService.getContractAndOrdersByContractNo(econtractRefEntityNo);
            Orders orders = contract.getOrders().stream().filter(order -> DEPART.getStatus() == order.getStatus()).findAny().orElseThrow(() -> new SubscribeException(DEPART_ORDER_NOT_FOUND));
            dropOffCarConfirm(orders.getOrderNo(), request, adminId);
        } else {
            DealerOrder dealerOrder = (DealerOrder) eContractRefEntity;
            if (!dealerOrder.getOrderStatus().equals(ContractStatus.GOING.getCode())) {
                throw new SubscribeException(DEALER_ORDER_STATUS_NOT_GOING);
            }
            dealerOrderService.closeDealerOrder(new DealerOrderCloseRequest(dealerOrder, request), adminId);
        }
    }

    /**
     * 還車資料確認
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void dropOffCarConfirm(String orderNo, CarDropOffRequest request, String adminId) {

        Orders order = this.getOrder(orderNo);
        Orders orderBeforeUpdate = deepCopy(order);

        Contract contract = order.getContract();
        MainContract mainContract = contract.getMainContract();
        boolean deletePriceInfo = true;
        long diffDay = DateUtil.calculateDiffDate(request.getReturnDate().toInstant(), order.getExpectEndDate(), DAYS);
        validateDropOffCarConfirmPreConditions(request, order, diffDay);

        if (Objects.equals(order.getReturnMileage(), request.getReturnMileage()) && Objects.equals(order.getEndDate(), request.getReturnDate().toInstant())) {
            deletePriceInfo = false;
        }

        // 還車備註改儲存至訂單備註
        appendRemark(order, request.getReturnRemark(), adminId);

        // 實際還車時間異動
        order.setEndDate(request.getReturnDate().toInstant());

        // 異動里程費
        List<OrderPriceInfo> mileageList = orderPriceInfoRepository.findAll((Specification<OrderPriceInfo>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.orderNo), orderNo));
            predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.category), MileageFee));
            predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.type), Pay.getCode()));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
        List<OrderPriceInfo> orderPriceInfoListBeforeUpdate = priceInfoService.getPriceInfosByOrder(orderNo).stream()
            .map(this::deepCopy).collect(Collectors.toList());

        mileageList = mileageList.stream().filter(orderPriceInfo -> orderPriceInfo.getReceivedAmount() == 0
            && !orderPriceInfo.isPaid()).sorted(Comparator.comparing(OrderPriceInfo::getStage)).collect(Collectors.toList());
        if (mileageList.isEmpty() && order.getReturnMileage() != null && !Objects.equals(order.getReturnMileage(), request.getReturnMileage())) {
            throw new SubscribeException(ORDER_PRICE_INFO_MILEAGE_FEE_HAS_PAYED);
        }
        mileageList.forEach(mileageOrderPriceInfo -> priceInfoService.calculateMillageFee(orderNo, mainContract.getAcctId(), request.getReturnMileage(), mileageOrderPriceInfo));
        order.setReturnMileage(request.getReturnMileage());

        boolean shouldProcessEtagReturn = orderPriceInfoRepository.findAll((Specification<OrderPriceInfo>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.orderNo), orderNo));
            predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.category), ETag));
            predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.type), Pay.getCode()));
            return builder.and(predicates.toArray(new Predicate[0]));
        }).isEmpty();

        // 若Etag未產生付費
        ETagInfo etagInfo = eTagService.getLatestNotReturnETagInfo(order, true);
        if (!shouldProcessEtagReturn && etagInfo != null && etagInfo.getETagPayFlow() == null) {
            shouldProcessEtagReturn = true;
        }

        // 沒有續約，則進行Etag還車
        ETagInfo etagInfoBeforeUpdate = null;
        List<OrderPriceInfo> orderPriceInfosToDelete = new ArrayList<>();
        if (((!RenewType.RENEW.equals(order.getRenewType()) && !RenewType.AUTO_RENEW.equals(order.getRenewType())) && StringUtils.isBlank(order.getNextStageOrderNo())) || shouldProcessEtagReturn) {
            // ETag還車
            if (etagInfo == null && !shouldProcessEtagReturn) {
                // 最新已還車
                ETagInfo returnedEtagInfo = eTagService.getLatestReturnETagInfo(order);
                if (returnedEtagInfo == null) {
                    throw new SubscribeException(ETAG_INFO_NOT_FUND);
                }
                if (order.getEndDate().isBefore(returnedEtagInfo.getReturnDate().truncatedTo(ChronoUnit.MINUTES))) {
                    throw new SubscribeException(LATEST_ETAG_PAID_RETURN_TIME_CAN_NOT_EARLIER_THAN_ETAG_END_TIME);
                }
            } else {
                if (etagInfo == null) {
                    throw new SubscribeException(ETAG_PAID_CAN_NOT_MODIFY_RETURN_TIME);
                }
                etagInfoBeforeUpdate = deepCopy(etagInfo);
                try {
                    etagInfo = eTagService.processAndUpdateETagInfoForCarReturn(order, mainContract.getPlateNo(), adminId, mainContract.getReturnStationCode(), etagInfo);
                } catch (SubscribeException e) {
                    if (ETAG_RENT_CAR_FAIL_WITHOUT_DEPART == e.getCode()) {
                        throw new SubscribeException(ETAG_PAID_CAN_NOT_MODIFY_RETURN_TIME);
                    }
                    throw e;
                }

                Integer orderPriceInfoId = etagInfo.getOrderPriceInfoId();
                OrderPriceInfo etagPriceInfo = null;
                if (orderPriceInfoId != null && orderPriceInfoId > 0) {
                    etagPriceInfo = orderPriceInfoRepository.getOne(orderPriceInfoId);
                }

                // ETag 紀錄 OrderPriceInfo
                if (!ETagPayFlow.DONE_NOT_NEED_PAY.getCode().equals(etagInfo.getETagPayFlow())) {
                    OrderPriceInfo upsertedEtagPriceInfo = new OrderPriceInfo();
                    upsertedEtagPriceInfo.setId(orderPriceInfoId);
                    upsertedEtagPriceInfo.setOrderNo(order.getOrderNo());
                    upsertedEtagPriceInfo.setStage(Math.max(DateUtil.calculateStageAndDate(order).size(), 1));
                    upsertedEtagPriceInfo.setLastPayDate(DateUtil.convertToEndOfInstant(Optional.ofNullable(order.getEndDate()).orElse(order.getExpectEndDate())));
                    upsertedEtagPriceInfo.setCategory(ETag);
                    upsertedEtagPriceInfo.setType(Pay.getCode());
                    upsertedEtagPriceInfo.setAmount(Optional.ofNullable(etagInfo.getETagAmt()).orElse(0));
                    upsertedEtagPriceInfo = addOrUpdate(upsertedEtagPriceInfo);
                    orderPriceInfosToDelete.add(Optional.ofNullable(etagPriceInfo).orElse(upsertedEtagPriceInfo));
                    etagInfo.setOrderPriceInfoId(upsertedEtagPriceInfo.getId());
                    etagInfoRepository.save(etagInfo);
                }
            }
        }
        // 更新車色
        if (StringUtils.isNotBlank(request.getColor())) {
            Cars car = carsService.findByPlateNo(mainContract.getPlateNo());
            car.setColorDesc(request.getColor());
            carsService.update(car);
        }
        // 提前還車
        boolean returnEarly = false;
        if (diffDay > 0 && deletePriceInfo) {
            returnEarly = true;
            priceInfoService.setReturnEarlyV2(order);
        } else if (diffDay < 0 && deletePriceInfo) {
            //延後還車
            priceInfoService.setReturnLate(order);
        }
        // 補收里程費，因有提提前延後還車清空其他費用機制，故放在提前/延後還車邏輯之後
        priceInfoService.rePayMileageFee(order, returnEarly, orderPriceInfosToDelete);
        if (returnEarly) {
            captureOrderStateBeforeReturnEarly(orderBeforeUpdate, orderPriceInfoListBeforeUpdate, etagInfoBeforeUpdate, orderPriceInfosToDelete);
        }
        // 還車資料確認後，所有未付款費用開放付款
        List<OrderPriceInfo> orderPriceInfoList = orderPriceInfoRepository.findAll((Specification<OrderPriceInfo>) (root, query, builder) -> builder.and(
                builder.equal(root.get(OrderPriceInfo.Fields.orderNo), orderNo), builder.equal(root.get(OrderPriceInfo.Fields.receivedAmount), 0))
            ).stream().peek(orderPriceInfo -> orderPriceInfo.setReceivableDate(convertToStartOfInstant(Instant.now())))
            .collect(Collectors.toList());
        orderPriceInfoRepository.saveAll(orderPriceInfoList);
        priceInfoService.checkIsUnpaid(order);
        orderRepository.save(order);
    }

    private void validateDropOffCarConfirmPreConditions(CarDropOffRequest request, Orders order, long diffDay) {
        // 訂單狀態必須為已出車未還車或失竊未結案狀態才可還車
        if (DEPART.getStatus() != order.getStatus() && STOLEN.getStatus() != order.getStatus()) {
            throw new SubscribeException(ORDER_STATUS_NOT_DEPART);
        }
        // 還車資料是否正確填寫
        if (request.getReturnDate().toInstant().isBefore(order.getStartDate())) {
            throw new SubscribeException(RETURN_TIME_SHOULD_MORE_THAN_START_TIME);
        }
        if (order.getDepartMileage().compareTo(request.getReturnMileage()) > 0) {
            throw new SubscribeException(DEPART_MILEAGE_SHOULD_NOT_GREATER_THAN_RETURN_MILEAGE);
        }
        //  當曾經付/退款後不可異動時間
        if (diffDay != 0 && isPaidRefundReturnCarEarlyOrLateFee(order.getOrderNo())) {
            throw new SubscribeException(ORDER_ALREADY_PAID_OR_REFUND_RETURN_CAR_FEE);
        }
    }

    private <T> T deepCopy(T original) {
        try {
            return (T) objectMapper.readValue(objectMapper.writeValueAsString(original), original.getClass());
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private void captureOrderStateBeforeReturnEarly(Orders orderBeforeUpdate,
                                                    List<OrderPriceInfo> orderPriceInfoList,
                                                    ETagInfo etagInfoBeforeUpdate,
                                                    List<OrderPriceInfo> orderPriceInfosToDelete
    ) {

        String orderNo = orderBeforeUpdate.getOrderNo();
        String redisKey = ORDER_STATE + orderNo;

        if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(redisKey))) {
            // Convert entities to DTOs
            OrdersDTO order = OrdersDTO.convertToDto(orderBeforeUpdate);
            List<OrderPriceInfoDTO> orderPriceInfoDTOList = orderPriceInfoList.stream().map(OrderPriceInfoDTO::convertToDto).collect(Collectors.toList());
            ETagInfoDTO etagInfo = ETagInfoDTO.convertToDto(etagInfoBeforeUpdate);
            List<OrderPriceInfoDTO> orderPriceInfoDTOsToDelete = orderPriceInfosToDelete.stream().map(OrderPriceInfoDTO::convertToDto).collect(Collectors.toList());

            // 將狀態保存到 Redis，設置過期時間為 30 天
            CachedReturnEarlyOrder state = new CachedReturnEarlyOrder(order, orderPriceInfoDTOList, etagInfo, orderPriceInfoDTOsToDelete);

            String stateJson;
            try {
                stateJson = objectMapper.writeValueAsString(state);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }

            stringRedisTemplate.opsForValue().set(redisKey, stateJson, 30, TimeUnit.DAYS);
        }
    }

    /**
     * 檢查是否曾經已付/退款項
     */
    private boolean isPaidRefundReturnCarEarlyOrLateFee(String orderNo) {
        return priceInfoService.getPriceInfosByOrder(orderNo).stream().anyMatch(orderPriceInfo ->
            (orderPriceInfo.getCategory().equals(ReturnEarly)
                || orderPriceInfo.getCategory().equals(ReturnLate)) && orderPriceInfo.getReceivedAmount() > 0);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public OrderPriceInfo addOrUpdate(OrderPriceInfo info) {
        try {
            OrderPriceInfo etagOrderPriceInfo = null;
            if (info.getId() != null && info.getId() > 0) {
                etagOrderPriceInfo = orderPriceInfoRepository.getOne(info.getId());
            }
            if (etagOrderPriceInfo != null) {
                etagOrderPriceInfo.setAmount(info.getAmount());
                info = orderPriceInfoRepository.save(etagOrderPriceInfo);
            } else {
                info = orderPriceInfoRepository.save(info);
            }
        } catch (Exception ex) {
            log.error("addOrUpdate error", ex);
            throw new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND);
        }
        return info;
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void undoDropOffCarConfirm(String orderNo) {

        String redisKey = ORDER_STATE + orderNo;

        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
            String stateJson = stringRedisTemplate.opsForValue().get(redisKey);
            if (stateJson != null) {
                log.info("orderState: {}", stateJson);
                CachedReturnEarlyOrder state;
                try {
                    state = objectMapper.readValue(stateJson, new TypeReference<CachedReturnEarlyOrder>() {
                    });
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }

                Orders dbOrder = getOrder(orderNo);

                // 處理款項明細
                List<OrderPriceInfo> cachedPriceInfos = state.getOrderPriceInfos().stream().map(OrderPriceInfoDTO::revertToEntity).collect(Collectors.toList());
                Map<Integer, OrderPriceInfo> dbOrderPriceInfoMap = priceInfoService.getPriceInfosByOrder(orderNo).stream().collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));
                AtomicBoolean isDbOrderPriceInfoPaidAfterReturnCarConfirm = new AtomicBoolean(false);
                List<OrderPriceInfo> priceInfosToRestore = cachedPriceInfos.stream()
                    .filter(cachedPriceInfo -> {
                        OrderPriceInfo dbOrderPriceInfo = dbOrderPriceInfoMap.get(cachedPriceInfo.getId());
                        if (dbOrderPriceInfo == null) {
                            return true;
                        }
                        if (dbOrderPriceInfo.isPaid() && !cachedPriceInfo.isPaid()) {
                            isDbOrderPriceInfoPaidAfterReturnCarConfirm.set(true);
                            return false;
                        }
                        return true;
                    }).collect(Collectors.toList());
                orderPriceInfoRepository.saveAll(priceInfosToRestore);

                // 處理訂單
                if (!isDbOrderPriceInfoPaidAfterReturnCarConfirm.get()) {
                    Orders cachedOrder = state.getOrder().revertToEntity();
                    dbOrder.setStatus(cachedOrder.getStatus());
                    dbOrder.setReturnMileage(cachedOrder.getReturnMileage());
                    dbOrder.setEndDate(cachedOrder.getEndDate());
                    dbOrder.setReturnRemark(cachedOrder.getReturnRemark());
                    dbOrder.setRemarks(cachedOrder.getRemarks());
                    dbOrder.setReportMileage(cachedOrder.getReportMileage());
                }

                ETagInfo cachedETagInfo = state.getEtagInfo().revertToEntity();
                state.getOrderPriceInfoDTOsToDelete().forEach(orderPriceInfoDTO -> {
                    OrderPriceInfo orderPriceInfoToDelete = priceInfoService.get(orderPriceInfoDTO.getId());
                    if (orderPriceInfoToDelete == null || orderPriceInfoToDelete.isPaid()) {
                        return;
                    }
                    orderPriceInfoRepository.delete(orderPriceInfoToDelete);
                    if (orderPriceInfoToDelete.getCategory() != ETag) {
                        return;
                    }
                    // 處理ETag
                    if (!etagInfoRepository.existsById(cachedETagInfo.getId())) {
                        throw new SubscribeException(ETAG_INFO_NOT_FUND);
                    }
                    etagInfoRepository.save(cachedETagInfo);
                });

                checkIsUnpaid(dbOrder);
                orderRepository.save(dbOrder);

                // Delete redisKey
                stringRedisTemplate.delete(redisKey);
            }
        } else {
            throw new SubscribeException(NOT_YET_RETURN_EARLY);
        }
    }

    /**
     * 還車90改為已還車未結案80
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders arriveStatusToArriveNoClose(String orderNo, AdminUser adminUser) {
        Orders order = this.getOrder(orderNo);

        Map<String, Stations> visibleStations = stationService.getAllVisibleStation(adminUser);
        // 該訂單之 還車站所 需為角色對應之可視站所
        if (!visibleStations.containsKey(order.getContract().getMainContract().getReturnStationCode())) {
            throw new AuthException();
        }

        if (order.getStatus() != CLOSE.getStatus()) {
            throw new SubscribeException(ORDER_STATUS_NOT_CLOSE);
        }

        Contract contract = order.getContract();
        MainContract mainContract = contract.getMainContract();
        order.setStatus(ARRIVE_NO_CLOSE.getStatus());
        orderRepository.save(order);
        if (contract.getStatus().equals(ContractStatus.COMPLETE.getCode())) {
            contract.setStatus(ContractStatus.GOING.getCode());
            contractRepository.save(contract);
        }
        if (mainContract.getStatus().equals(ContractStatus.COMPLETE.getCode())) {
            mainContract.setStatus(ContractStatus.GOING.getCode());
            mainContractRepository.save(mainContract);
        }

        eventPublisher.publishEvent(new OrderReopenedEvent(this, order, adminUser.getMemberId()));

        return order;
    }


    /**
     * 還車98改為待審核1
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders creditFailToPending(String orderNo, AdminUser adminUser) {
        Orders order = this.getOrder(orderNo);

        Contract contract = order.getContract();
        MainContract mainContract = contract.getMainContract();

        Map<String, Stations> visibleStations = stationService.getAllVisibleStation(adminUser);
        // 該訂單之 還車站所 需為角色對應之可視站所
        if (!visibleStations.containsKey(order.getContract().getMainContract().getReturnStationCode())) {
            throw new AuthException();
        }

        if (order.getStatus() != CREDIT_REJECT.getStatus()) {
            throw new SubscribeException(ORDER_STATUS_NOT_REJECT);
        }

        // 檢查是否已經有續約成功的訂單
        if (!order.getIsNewOrder()) {
            List<Contract> contracts = contractRepository.getContractsByMainContractNo(mainContract.getMainContractNo());
            // 新得合約stage會變-1
            if (contract.getStage() < 0) {
                if (contracts.stream().anyMatch(c -> c.getStage() == Math.abs(contract.getStage()) && !c.getContractNo().equals(contract.getContractNo()))) {
                    throw new SubscribeException(ALREADY_CREDIT_APPROVE_ORDER);
                }
            } else {
                List<Orders> ordersList = contractRepository.getContractOrders(contract.getContractNo()).getOrders();
                if (ordersList.stream().anyMatch(o -> o.getStage() == Math.abs(order.getStage()) && !o.getOrderNo().equals(order.getOrderNo()))) {
                    throw new SubscribeException(ALREADY_CREDIT_APPROVE_ORDER);
                }
            }
        }

        order.setStatus(CREDIT_PENDING.getStatus());
        order.setStage(Math.abs(order.getStage()));

        orderRepository.save(order);
        if (contract.getStatus().equals(ContractStatus.CANCEL.getCode())) {
            contract.setStatus(ContractStatus.CREATE.getCode());
            contract.setStage(Math.abs(contract.getStage()));
            contractRepository.save(contract);
        }
        if (mainContract.getStatus().equals(ContractStatus.CANCEL.getCode())) {
            mainContract.setStatus(ContractStatus.CREATE.getCode());
            mainContractRepository.save(mainContract);
        }
        if (!order.getIsNewOrder()) {
            recordRenewTypeToPreOrder(orderNo, RenewType.RENEW);
        }

        eventPublisher.publishEvent(new OrderCreditPendingEvent(this, order, adminUser.getMemberId()));

        return order;
    }

    /**
     * 產生還車任務
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void generateReturnTask(String memberId, String orderNo, CarDropOffCompleteRequest request) {

        Orders order = this.getOrder(orderNo);
        Contract contract = order.getContract();

        // 檢查出租單類型
        boolean isPaperRental = RentalFormType.PAPER.equals(contract.getRentalFormType());

        // 如果是電子出租單模式，檢查並處理現有任務
        if (!isPaperRental) {
            if (StringUtils.isNotBlank(contract.getReturnTaskId())) {
                TaskDetailResponse detailResponse = rentalTaskService.getTaskDetail(contract.getReturnTaskId());
                if (detailResponse.getStatus() == 7) {
                    rentalTaskService.deleteTask(order, TaskType.RETURN);
                } else if (detailResponse.getStatus() < 7) {
                    rentalTaskService.resetTask(Integer.valueOf(contract.getReturnTaskId()));
                } else {
                    contract.setReturnTaskId(null);
                }
            }

            if (RenewType.RENEW != order.getRenewType() && StringUtils.isNotBlank(contract.getDepartTaskId()) && StringUtils.isBlank(contract.getReturnTaskId())) {
                // 產生還車任務
                rentalTaskService.generateTask(order, TaskType.RETURN);
            }
            contractService.updateContract(contract);
        } else {
            // 紙本出租單模式直接執行還車邏輯
            dropOffCar(memberId, orderNo, request);
        }
    }

    /**
     * 還車
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void dropOffCar(String headerMemberId, String orderNo, CarDropOffCompleteRequest request) {
        Orders order = this.getOrder(orderNo);
        Contract contract = order.getContract();
        MainContract mainContract = contract.getMainContract();

        AuthUser authUser = authServer.getUserWithRetry(mainContract.getAcctId());
        // 檢查還車人員
        validateAndSetReturnMember(request.getReturnMemberId(), order);

        // 檢查 所有須繳費用皆已繳納
        OrderPriceInfoCriteria criteria = new OrderPriceInfoCriteria();
        criteria.setOrderNo(Collections.singletonList(orderNo));
        criteria.setType(Pay.getCode());
        List<OrderPriceInfo> list = orderPriceInfoRepository.getPriceInfos(criteria);
        // 開放所有未開放繳款費用
        if (updateUnreceivableDates(list)) {
            orderPriceInfoRepository.updateOrderPriceInfos(list);
            throw new SubscribeException(ORDER_PRICE_INFO_UNRECEIVABLE_NOT_ALLOW_RETURN);
        }
        for (OrderPriceInfo info : list) {
            if (info.getAmount() != 0 && (info.getReceivedAmount() == null || info.getReceivedAmount() == 0)) {
                switch (info.getCategory()) {
                    case SecurityDeposit:
                        throw new SubscribeException(ORDER_PRICE_INFO_SECURITY_DEPOSIT_NON_PAYMENT);
                    case MonthlyFee:
                        throw new SubscribeException(ORDER_PRICE_INFO_MONTHLY_FEE_NON_PAYMENT);
                    case MileageFee:
                        if (order.getContract().getStatus() != ContractStatus.CREATE.getCode()) {
                            throw new SubscribeException(ORDER_PRICE_INFO_MILEAGE_FEE_NON_PAYMENT);
                        }
                        break;
                    case Insurance:
                        throw new SubscribeException(ORDER_PRICE_INFO_INSURANCE_NON_PAYMENT);
                    case Dispatch:
                        throw new SubscribeException(ORDER_PRICE_INFO_DISPATCH_NON_PAYMENT);
                    case ETag:
                        // 是否有最後一筆訂單
                        if (isLastOrder(order)) {
                            throw new SubscribeException(ORDER_PRICE_INFO_ETAG_NON_PAYMENT);
                        }
                        break;
                    default:
                }
            }
        }
        // 收支不平衡
        if (!paymentService.checkBalance(order.getOrderNo())) {
            log.error("還車收支不平衡 orderId: {}", order.getOrderNo());
            throw new SubscribeException(ORDER_PRICE_INFO_AND_AMOUNT_NOT_BALANCE);
        }
        // ETag close
        eTagService.close(order, mainContract.getPlateNo(), request.getReturnMemberId(), mainContract.getReturnStationCode(), true);
        Cars car = carsService.findByPlateNo(mainContract.getPlateNo());
        // 還車里程
        car.setCurrentMileage(order.getReturnMileage());
        carsService.update(car);

        // 有無車損
        boolean hasAccident = OrderUtils.hasAccident(order);
        if (hasAccident) {
            order.setStatus(ARRIVE_NO_CLOSE.getStatus());
            car.setLaunched(CarDefine.Launched.accident);
        } else {
            order.setStatus(CLOSE.getStatus());
            // 是否有最後一筆訂單
            if (isLastOrder(order)) {
                contract.setStatus(ContractStatus.COMPLETE.getCode());
                mainContract.setStatus(ContractStatus.COMPLETE.getCode());
            } else {
                Orders nextOrder = getOrder(order.getNextStageOrderNo());
                if (!nextOrder.getContract().getContractNo().equals(contract.getContractNo())) {
                    contract.setStatus(ContractStatus.COMPLETE.getCode());
                }
            }
        }

        // 不管車損與否，更新最後一筆訂單時間到合約、主約
        if (isLastOrder(order)) {
            contract.setEndDate(order.getEndDate());
            mainContract.setEndDate(order.getEndDate());
        } else {
            Orders nextOrder = getOrder(order.getNextStageOrderNo());
            if (!nextOrder.getContract().getContractNo().equals(contract.getContractNo())) {
                contract.setEndDate(order.getEndDate());
            }
        }
        contractService.updateContract(contract);
        contractService.updateMainContract(mainContract);

        order.setCloseDate(Instant.now());
        order.setCloseUser(headerMemberId);
        orderRepository.save(order);

        try {
            checkoutService.checkOut(order);
        } catch (BadRequestException | SubscribeException e) {
            log.error("立帳失敗,orderNo:{}", order.getOrderNo(), e);
        }

        // 退款
        List<OrderPriceInfo> refundList = orderPriceInfoRepository.findAll((Specification<OrderPriceInfo>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.orderNo), orderNo));
            predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.type), Refund.getCode()));
            predicates.add(builder.greaterThan(root.get(OrderPriceInfo.Fields.amount), 0));
            predicates.add(builder.isNull(root.get(OrderPriceInfo.Fields.paymentId)));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
        if (!refundList.isEmpty()) {
            OrderPriceInfoRefundRetryRequest refundRetryRequest = new OrderPriceInfoRefundRetryRequest();
            refundRetryRequest.setRefundIds(refundList.stream().map(OrderPriceInfo::getId).collect(Collectors.toList()));
            // 因已登打及退款，故還車時不退款避免重複退款
            // TODO 檢查登打退款無誤此區塊即可刪除
//            try {
//                paymentService.refundOrderPriceInfosRetry(refundRetryRequest);
//            } catch (JsonProcessingException e) {
//                log.error("dropOffCar json error", e);
//            }
        }
        if (isLastOrder(order)) {
            // 放車
            carsService.releaseCar(order, car);
            order = generateCenterContract(order, car, true);

            buChangeService.changeReturn(order, crsService.getCar(car.getPlateNo()), request.getReturnMemberId(), car);
            carsService.updateLocationStationBasedOnSgType(mainContract, car);
            carsService.update(car);

            orderRepository.save(order);
            if (hasAccident) {
                notifyService.notifyReturnNotClose(order, authUser);
            } else {
                refundSecurityDeposit(mainContract);
                notifyService.notifyReturnCar(order, authUser);
            }
        } else {
            // 續約訂單設定出車時間與里程
            setNextStageDepartDateAndMileage(order);
        }

        eventPublisher.publishEvent(new OrderReturnedEvent(this, order, headerMemberId));

        // 向 CRS 更新車輛最新里程數
        crsService.updateKm(car, car.getCurrentMileage(), order.getOrderNo());

        // 出租單 && 合約 合併寄送
        econtractService.asyncGenRentalTaskPdfSendECFilesMail(order, contract, mainContract, headerMemberId, TaskType.RETURN);
    }

    private boolean updateUnreceivableDates(List<OrderPriceInfo> orderPriceInfos) {
        boolean updated = false;
        Instant now = convertToStartOfInstant(Instant.now());

        for (OrderPriceInfo info : orderPriceInfos) {
            if (!info.isPaid() && info.getReceivableDate().isAfter(Instant.now())) {
                info.setReceivableDate(now);
                updated = true;
            }
        }

        return updated;
    }

    private CarDropOffRequest getCarDropOffRequest(LegalOperationRequest legalOperationRequest, Orders order) {
        CarDropOffRequest carDropOffRequest = new CarDropOffRequest();

        // 設定還車時間
        Date returnDate = Optional.ofNullable(legalOperationRequest.getReturnDate())
            .orElseGet(() -> Optional.ofNullable(order.getEndDate())
                .map(Date::from)
                .orElseThrow(() -> new SubscribeException(RETURN_TIME_CAN_NOT_BE_NULL)));
        carDropOffRequest.setReturnDate(returnDate);

        // 設定還車里程
        Integer returnMileage = Optional.ofNullable(legalOperationRequest.getReturnMileage())
            .orElseGet(() -> Optional.ofNullable(order.getReturnMileage())
                .orElseThrow(() -> new SubscribeException(RETURN_MILEAGE_CAN_NOT_BE_NULL)));
        carDropOffRequest.setReturnMileage(returnMileage);

        return carDropOffRequest;
    }

    /**
     * 還車 - 法務作業
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void dropOffCarForLegalOperation(Orders order, CarDropOffCompleteRequest request, LegalOperationReason reason, String headerMemberId) {

        Contract contract = order.getContract();
        MainContract mainContract = contract.getMainContract();
        AuthUser authUser = authServer.getUserWithRetry(mainContract.getAcctId());

        // 檢查還車人員
        validateAndSetReturnMember(request.getReturnMemberId(), order);

        // 更新還車里程
        Cars car = carsService.findByPlateNo(mainContract.getPlateNo());
        car.setCurrentMileage(order.getReturnMileage());
        carsService.update(car);

        order = checkIsUnpaid(order);

        int targetOrderStatus = inferTargetOrderStatus(order, reason);
        order.setStatus(targetOrderStatus);

        boolean shouldCloseOrder = CLOSE.getStatus() == order.getStatus();
        if (shouldCloseOrder) {
            order.setCloseDate(Instant.now());
            order.setCloseUser(headerMemberId);

            // 是否有最後一筆訂單
            if (isLastOrder(order)) {
                contract.setStatus(ContractStatus.COMPLETE.getCode());
                contract.setEndDate(order.getEndDate());
                contractService.updateContract(contract);
                mainContract.setStatus(ContractStatus.COMPLETE.getCode());
                mainContract.setEndDate(order.getEndDate());
                contractService.updateMainContract(mainContract);
            } else {
                Orders nextOrder = getOrder(order.getNextStageOrderNo());
                if (!nextOrder.getContract().getContractNo().equals(contract.getContractNo())) {
                    contract.setStatus(ContractStatus.COMPLETE.getCode());
                    contract.setEndDate(order.getEndDate());
                    contractService.updateContract(contract);
                }
            }
        }

        if (isLastOrder(order)) {
            order = generateCenterContract(order, car, true);
            buChangeService.changeReturn(order, crsService.getCar(car.getPlateNo()), request.getReturnMemberId(), car);
            // 依照不同法務事由設定車輛狀態
            car.setCarStatus(reason.getCarStatus().getCode());
            if (reason.getCarStatus() == CarDefine.CarStatus.Scrapped) {
                car.setLaunched(CarDefine.Launched.accident);
            }
            carsService.updateLocationStationBasedOnSgType(mainContract, car);

            if (shouldCloseOrder) {
                refundSecurityDeposit(mainContract);
                notifyService.notifyReturnCar(order, authUser);
            } else {
                notifyService.notifyReturnNotClose(order, authUser);
            }
        } else {
            // 續約訂單設定出車時間與里程
            setNextStageDepartDateAndMileage(order);
        }
        orderRepository.save(order);

        // 向 CRS 更新車輛最新里程數
        crsService.updateKm(car, car.getCurrentMileage(), order.getOrderNo());

        // 出租單 && 合約 合併寄送
        econtractService.asyncGenRentalTaskPdfSendECFilesMail(order, contract, mainContract, headerMemberId, TaskType.RETURN);
    }

    public int inferTargetOrderStatus(Orders order, LegalOperationReason reason) {
        return ((reason == RETURNED_WITH_DAMAGE || reason == RETURNED_WITH_PAID) && (order.getIsUnpaid() || OrderUtils.hasAccident(order)))
            ? (order.getIsUnpaid() || (OrderUtils.hasAccident(order) && "Y".equals(order.getAccidentInfo().getReturnNego()))) ? ARRIVE_NO_CLOSE.getStatus() : CLOSE.getStatus()
            : reason.getOrderStatus().getStatus();
    }

    /**
     * 驗證還車人員並將訂單設定還車人員
     */
    private void validateAndSetReturnMember(String returnMemberId, Orders order) {
        validateMemberExists(returnMemberId);
        order.setReturnMemberId(returnMemberId);
    }

    private void validateMemberExists(String memberId) {
        List<MemberInfo> memberInfos = authorityServer.getMemberInfos(memberId);
        if (memberInfos.isEmpty()) {
            throw new SubscribeException(MEMBER_INFO_NOT_FOUND);
        }
    }

    /**
     * 設定續約單當前訂單還車時間及里程
     */
    private void setNextStageDepartDateAndMileage(Orders order) {
        String nextStageOrderNo = order.getNextStageOrderNo();
        Orders nextOrders = getOrder(nextStageOrderNo);
        nextOrders.setStartDate(nextOrders.getExpectStartDate());
        nextOrders.setDepartMileage(order.getReturnMileage());
        updateOrder(nextOrders);
    }

    public void refundSecurityDeposit(MainContract mainContract) {
        try {
            Set<String> ordersNos = getRelatedOrderNos(mainContract);
            boolean isChangeCarRenew = checkIsChangeCarRenew(ordersNos);
            // 取得退款保證金 OrderPriceInfo
            List<OrderPriceInfo> paidOrderPriceInfos = getPaidSecurityDepositPriceInfos(new ArrayList<>(ordersNos));

            if (!paidOrderPriceInfos.isEmpty()) {
                OrderPriceInfo paidOrderPriceInfo = paidOrderPriceInfos.get(0);
                List<OrderPriceInfo> refundList = priceInfoService.refundAll(paidOrderPriceInfo.getId(), PriceInfoDefinition.PriceInfoCategory.SecurityDeposit);
                OrderPriceInfoRefundRetryRequest refundRetryRequest = new OrderPriceInfoRefundRetryRequest();
                refundRetryRequest.setRefundIds(refundList.stream().map(OrderPriceInfo::getId).collect(Collectors.toList()));
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        // 交易提交後執行
                        try {
                            paymentService.refundOrderPriceInfosRetry(refundRetryRequest);
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
                if (isChangeCarRenew) {
                    mattermostServer.notify("換車保證金發動退款", new SingletonMap<>("退款訂單", refundList.stream().map(OrderPriceInfo::getOrderNo).collect(Collectors.toList())), null);
                }
            } else {
                mattermostServer.notify("還車卻沒找到保證金發動退款", new SingletonMap<>("主約號碼", mainContract.getMainContractNo()), null);
            }
        } catch (Exception e) {
            Map<String, Object> info = new LinkedHashMap<>();
            info.put("主約號碼", mainContract.getMainContractNo());
            mattermostServer.notify("保證金發動退款失敗", info, e);
        }
    }

    /**
     * 取得所有相關訂單編號(包含換車訂單)
     */
    private Set<String> getRelatedOrderNos(MainContract mainContract) {
        Set<String> ordersNos = new HashSet<>();

        // 取得主約下所有訂單編號
        mainContract.getContracts().stream()
            .map(c -> getOrdersByContractNo(c.getContractNo()))
            .forEach(orders -> orders.forEach(o -> ordersNos.add(o.getOrderNo())));

        // 檢查並加入換車相關訂單
        for (String orderNo : new ArrayList<>(ordersNos)) {
            Orders order = getOrder(orderNo);
            if (isChangeCarRenew(order)) {
                mattermostServer.notify("有換車保證金發動退款",
                    new SingletonMap<>("短租母約編號", order.getSrentalParentOrderNo()), null);

                ordersNos.addAll(orderRepository.getOrdersBySrentalParentOrderNo(order.getSrentalParentOrderNo())
                    .stream()
                    .map(Orders::getOrderNo)
                    .collect(Collectors.toList()));
                break;
            }
        }

        return ordersNos;
    }

    /**
     * 檢查是否為換車訂單
     */
    private boolean isChangeCarRenew(Orders order) {
        return Optional.ofNullable(order.getIsChangeCarRenew()).orElse(false);
    }

    /**
     * 檢查訂單集合中是否包含換車訂單
     */
    private boolean checkIsChangeCarRenew(Set<String> ordersNos) {
        return ordersNos.stream()
            .map(this::getOrder)
            .anyMatch(this::isChangeCarRenew);
    }

    /**
     * 取得已付保證金清單
     */
    private List<OrderPriceInfo> getPaidSecurityDepositPriceInfos(List<String> ordersNos) {
        OrderPriceInfoCriteria criteria = new OrderPriceInfoCriteria();
        criteria.setOrderNo(ordersNos);
        criteria.setStage(1);
        criteria.setType(Pay.getCode());
        criteria.setCategory(Collections.singletonList(PriceInfoDefinition.PriceInfoCategory.SecurityDeposit));

        return orderPriceInfoRepository.getPriceInfos(criteria)
            .stream()
            .filter(priceInfo -> priceInfo.getReceivedAmount() > 0)
            .collect(Collectors.toList());
    }

    /**
     * 是否最後一筆訂單
     */
    public boolean isLastOrder(Orders order) {
        if (order.getNextStageOrderNo() == null) {
            return true;
        } else {
            return Objects.equals(RenewType.CANCEL, order.getRenewType())
                || Objects.equals(RenewType.PENDING, order.getRenewType())
                || Objects.equals(RenewType.WILLING, order.getRenewType())
                || order.getRenewType() == null;
        }
    }

    /**
     * 透過車牌取得狀態小於50的訂單
     */
    public List<OrderQueryResponse> getBookingAndDepartOrdersByPlateNos(Collection<String> plateNos) {
        return getOrdersByPlateNosAndStatus(plateNos, Arrays.stream(OrderStatus.values()).filter(o -> o.getStatus() <= DEPART.getStatus()).collect(Collectors.toList()));
    }

    /**
     * 透過車牌碼取得使用中的訂單
     */
    public List<OrderQueryResponse> getProcessOrdersByPlateNos(List<String> plateNos) {
        return getOrdersByPlateNosAndStatus(plateNos, Arrays.asList(
            BOOKING,
            DEPART,
            STOLEN,
            CLOSE_WITH_SUB));
    }

    public List<OrderQueryResponse> getOrdersByPlateNosAndStatus(Collection<String> plateNos, List<OrderStatus> status) {
        if (plateNos == null || plateNos.isEmpty()) {
            return new ArrayList<>();
        }
        OrdersCriteria ordersCriteria = new OrdersCriteria();
        ordersCriteria.setStatus(status.stream().map(OrderStatus::getStatus).collect(Collectors.toList()));
        ordersCriteria.setPlateNo(plateNos);
        return orderRepository.findBySearch(ordersCriteria, Integer.MAX_VALUE, 0).stream().map(OrderQueryResponse::new
        ).collect(Collectors.toList());
    }

    /**
     * 透過車牌碼取得使用中的訂單 (排除 orderNo)
     */
    public List<OrderQueryResponse> getProcessOrdersByPlateNoExcludingOrderNo(String plateNo, String excludingOrderNo) {
        return getProcessOrdersByPlateNos(Collections.singletonList(plateNo)).stream()
            .filter(o -> !Objects.equals(o.getOrderNo(), excludingOrderNo)).collect(Collectors.toList());
    }

    /**
     * 取得使用者進行中訂單數量
     */
    public long getUserProcessOrdersCount(int acctId) {
        OrdersCriteria ordersCriteria = new OrdersCriteria();
        ordersCriteria.setStatus(Collections.singletonList(
            CREDITED.getStatus()));
        ordersCriteria.setAcctId(Collections.singletonList(acctId));
        Set<String> orderNos = orderRepository.findBySearch(ordersCriteria, null, null).stream().map(obj -> ((Orders) Optional.ofNullable(obj.getOrders()).get()).getOrderNo()).collect(Collectors.toSet());
        ordersCriteria = new OrdersCriteria();
        ordersCriteria.setStatus(Collections.singletonList(
            DEPART.getStatus()));
        ordersCriteria.setAcctId(Collections.singletonList(acctId));
        orderNos.addAll(orderRepository.findBySearch(ordersCriteria, null, null).stream().map(obj -> ((Orders) Optional.ofNullable(obj.getOrders()).get()))
            .filter(o -> (!RenewType.RENEW.equals(o.getRenewType()) && !Objects.equals(RenewType.AUTO_RENEW, o.getRenewType())) && StringUtils.isBlank(o.getNextStageOrderNo())).map(Orders::getOrderNo).collect(Collectors.toList()));
        ordersCriteria = new OrdersCriteria();
        ordersCriteria.setIsUnpaid(true);
        ordersCriteria.setAcctId(Collections.singletonList(acctId));
        orderNos.addAll(orderRepository.findBySearch(ordersCriteria, null, null).stream().map(obj -> ((Orders) Optional.ofNullable(obj.getOrders()).get()).getOrderNo()).collect(Collectors.toList()));
        return orderNos.size();
    }

    /**
     * 逾期還車 檢視
     */
    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public ReturnLateCalculateResponse dropOffCarLatelyView(String orderNo) {

        Orders order = this.getOrder(orderNo);
        return dropOffCarLatelyView(order);
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public ReturnLateCalculateResponse dropOffCarLatelyView(Orders order) {

        Contract contract = order.getContract();
        MainContract mainContract = contract.getMainContract();
        // 延後天數
        int delayDays = (int) DateUtil.calculateDiffDate(order.getExpectEndDate(), order.getEndDate(), DAYS);

        // 罰金預估應收金額
        int monthlyFee = mainContract.getOriginalPriceInfo().getUseMonthlyFee();
        int insuranceFee = Optional.ofNullable(order.getContract()).map(Contract::getDisclaimer).map(disClaimer -> disClaimer ? order.getContract().getMainContract().getOriginalPriceInfo().getDisclaimerFee() : 0).orElse(0);

        Integer fineAmount = (int) Math.ceil((monthlyFee + insuranceFee) * 0.2 * delayDays);

        ReturnLateCalculateResponse response = new ReturnLateCalculateResponse();
        response.setDelayDays(delayDays);
        response.setSumAmount(fineAmount);
        return response;
    }

    private void handleNonCancelBookingDiscount(String orderNo, CarDropOffDiscountRequest request, AtomicReference<Integer> amount, List<OrderPriceInfo> negativeOrderPriceInfoList) {
        // 有指定費用明細
        if (request.getPriceInfoPayId() != null) {
            handleSpecificPriceInfo(orderNo, request, amount, negativeOrderPriceInfoList);
        } else { // 不指定費用明細
            handleUnspecifiedPriceInfo(orderNo, request, amount, negativeOrderPriceInfoList);
        }
    }

    private void handleSpecificPriceInfo(String orderNo, CarDropOffDiscountRequest request, AtomicReference<Integer> amount, List<OrderPriceInfo> negativeOrderPriceInfoList) {
        OrderPriceInfo paidOrderPriceInfo = priceInfoService.get(request.getPriceInfoPayId());
        if (!orderNo.equals(paidOrderPriceInfo.getOrderNo())) {
            throw new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND);
        }
        if (!CollectionUtils.isEmpty(paidOrderPriceInfo.getRemitAccountIds()) && !request.isForceRemitRefund()) {
            throw new SubscribeException(ORDER_PRICE_INFO_ALREADY_PAID_BY_REMIT);
        }
        OrderPriceInfo negativeOrderPriceInfo = priceInfoService.generateNegativeOrderPriceInfo(paidOrderPriceInfo, amount.get(), request.getCategory());
        amount.set(amount.get() - negativeOrderPriceInfo.getAmount());
        if (amount.get() <= 0) {
            negativeOrderPriceInfo.setInfoDetail(paidOrderPriceInfo.getInfoDetail() != null
                ? BeanUtils.copyProperties(paidOrderPriceInfo.getInfoDetail(), new PriceInfoDetail())
                : new PriceInfoDetail());
            negativeOrderPriceInfoList.add(negativeOrderPriceInfo);
        } else {
            throw new SubscribeException(REFUND_AMOUNT_OVER_PAY_AMOUNT);
        }
    }

    private void handleUnspecifiedPriceInfo(String orderNo, CarDropOffDiscountRequest request, AtomicReference<Integer> amount, List<OrderPriceInfo> negativeOrderPriceInfoList) {
        negativeOrderPriceInfoList.addAll(priceInfoService.discountAndSplitAmount(orderNo, amount, request.getCategory()));
        if (amount.get() > 0) {
            negativeOrderPriceInfoList.addAll(priceInfoService.refundAndSplitAmount(orderNo, amount, null, request.getCategory()));
        }
    }

    private boolean processNegativeOrderPriceInfos(CarDropOffDiscountRequest request, String adminId, List<OrderPriceInfo> negativeOrderPriceInfoList, String uid, Orders order, boolean notify) {
        return processNegativeOrderPriceInfos(request, request.getDiscount(), adminId, negativeOrderPriceInfoList, uid, order, notify);
    }

    private boolean processNegativeOrderPriceInfos(CarDropOffDiscountRequest request, int totalDiscount, String adminId, List<OrderPriceInfo> negativeOrderPriceInfoList, String uid, Orders order, boolean notify) {
        for (OrderPriceInfo negativeOrderPrice : negativeOrderPriceInfoList) {
            negativeOrderPrice.setUid(uid);
            PriceInfoDetail detail = Optional.ofNullable(negativeOrderPrice.getInfoDetail()).orElse(new PriceInfoDetail());
            setCommonDetails(request, totalDiscount, adminId, detail);

            switch (request.getCategory()) {
                case ReturnEarly:
                    // 延後/提前天數
                    notify = processReturnEarly(request, order, notify, detail);
                    break;
                case ReturnLate:
                    // 延後/提前天數
                    notify = processReturnLate(request, order, notify, detail);
                    break;
                default:
                    break;
            }
            negativeOrderPrice.setInfoDetail(detail);
        }
        return notify;
    }

    private void setCommonDetails(CarDropOffDiscountRequest request, int discount, String adminId, PriceInfoDetail detail) {
        detail.setOriginAmount(request.getOriginAmount());
        detail.setDiscount(discount);
        detail.setReason(request.getReason());
        detail.setDecideRemark(request.getDecideRemark());
        detail.setAdminId(adminId);
        detail.setIsAgree(request.isAgree() ? true : null);
        detail.setOriginalDiscount(request.getOriginalDiscount());
    }

    private boolean processReturnEarly(CarDropOffDiscountRequest request, Orders order, boolean notify, PriceInfoDetail detail) {
        detail.setDay((int) DateUtil.calculateDiffDate(order.getStartDate(), order.getExpectEndDate(), DAYS));
        int earlyDays = (int) DateUtil.calculateDiffDate(order.getExpectEndDate(), order.getEndDate(), DAYS);
        detail.setOriginAmount(request.getOriginAmount());
        detail.setDiscount(request.getDiscount());
        detail.setReason(request.getReason());
        detail.setDelayDays(earlyDays);
        if (Math.round(request.getOriginAmount() * 0.2) >= request.getDiscount()) {
            detail.setIsAgree(true);
            notify = false;
        }
        return notify;
    }

    private boolean processReturnLate(CarDropOffDiscountRequest request, Orders order, boolean notify, PriceInfoDetail detail) {
        detail.setDay((int) DateUtil.calculateDiffDate(order.getStartDate(), order.getExpectEndDate(), DAYS));
        int delayDays = (int) DateUtil.calculateDiffDate(order.getExpectEndDate(), order.getEndDate(), DAYS);
        detail.setDelayDays(delayDays);
        detail.setFineFrom(request.getCategory().name());
        if (Math.round(request.getOriginAmount() * 0.04) >= request.getDiscount()) {
            detail.setIsAgree(true);
            notify = false;
        }
        return notify;
    }

    private void handleSpecialCategories(String orderNo, CarDropOffDiscountRequest request) {
        if (Objects.equals(request.getCategory(), ReturnEarly) || Objects.equals(request.getCategory(), ReturnLate)) {
            orderPriceInfoRepository.deleteNotPaidEmpDiscardFee(orderNo);
        }
    }

    private void saveAndNotify(CarDropOffDiscountRequest request, List<OrderPriceInfo> negativeOrderPriceInfoList, Orders order, boolean notify) {
        orderPriceInfoRepository.saveAll(negativeOrderPriceInfoList);
        priceInfoService.checkIsUnpaid(order);
        orderRepository.save(order);
        if (notify && !request.isAgree()) {
            notifyService.addEmpDiscountRequest(order, negativeOrderPriceInfoList, request.getCategory());
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void processDropOffCarDiscount(String orderNo, CarDropOffDiscountRequest request, String adminId, Orders order) {

        List<OrderPriceInfo> negativeOrderPriceInfoList = new ArrayList<>();
        boolean notify = true;

        request.setOriginalDiscount(Optional.ofNullable(request.getOriginalDiscount()).orElse(request.getDiscount()));
        AtomicReference<Integer> amount = new AtomicReference<>(request.getDiscount());

        if (request.getCategory() == CancelBooking) {
            negativeOrderPriceInfoList.add(priceInfoService.refundCancelOrderPriceInfo(order, request));
        } else {
            handleNonCancelBookingDiscount(orderNo, request, amount, negativeOrderPriceInfoList);
        }

        String uid = StringUtils.isNotBlank(request.getUid()) ? request.getUid() : UUID.randomUUID().toString();
        notify = processNegativeOrderPriceInfos(request, adminId, negativeOrderPriceInfoList, uid, order, notify);

        // 門市折扣
        handleSpecialCategories(orderNo, request);

        saveAndNotify(request, negativeOrderPriceInfoList, order, notify);
    }

    /**
     * 還車共用產生折扣 且寄信通知主管
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public List<OrderPriceInfo> dropOffCarDiscount(String orderNo, CarDropOffDiscountRequest request, String adminId) {

        Orders order = this.getOrder(orderNo);

        processDropOffCarDiscount(orderNo, request, adminId, order);

        return priceInfoService.getPriceInfosByOrder(orderNo);
    }

    /**
     * 還車共用產生多筆折扣 且寄信通知主管
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public List<OrderPriceInfo> dropOffCarDiscounts(String orderNo, List<CarDropOffDiscountRequest> requests, String adminId) {

        if (requests == null || requests.isEmpty()) {
            throw new IllegalArgumentException("請求不得為空");
        }

        Orders order = this.getOrder(orderNo);
        CarDropOffDiscountRequest discountRequest = requests.get(0);
        String uid = StringUtils.isNotBlank(discountRequest.getUid()) ? discountRequest.getUid() : UUID.randomUUID().toString();
        List<OrderPriceInfo> allNegativeOrderPriceInfoList = new ArrayList<>();
        boolean notify = true;

        // sum all requests discount as totalDiscount
        int totalDiscount = requests.stream().mapToInt(CarDropOffDiscountRequest::getDiscount).sum();

        for (CarDropOffDiscountRequest request : requests) {

            List<OrderPriceInfo> negativeOrderPriceInfoList = new ArrayList<>();

            request.setOriginalDiscount(Optional.ofNullable(request.getOriginalDiscount()).orElse(request.getDiscount()));
            AtomicReference<Integer> amount = new AtomicReference<>(request.getDiscount());

            if (request.getCategory() == CancelBooking) {
                negativeOrderPriceInfoList.add(priceInfoService.refundCancelOrderPriceInfo(order, request));
            } else {
                handleNonCancelBookingDiscount(orderNo, request, amount, negativeOrderPriceInfoList);
            }

            notify = processNegativeOrderPriceInfos(request, totalDiscount, adminId, negativeOrderPriceInfoList, uid, order, notify);

            // 門市折扣
            handleSpecialCategories(orderNo, request);

            allNegativeOrderPriceInfoList.addAll(negativeOrderPriceInfoList);
        }

        saveAndNotify(discountRequest, allNegativeOrderPriceInfoList, order, notify);

        return priceInfoService.getPriceInfosByOrder(orderNo);
    }

    /**
     * 還車共用產生折扣修改 且寄信通知主管
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public List<OrderPriceInfo> updateDropOffCarDiscount(String uid, CarDropOffDiscountRequest request, String adminId) {
        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getByUid(uid);
        if (orderPriceInfoList == null || orderPriceInfoList.isEmpty()) {
            throw new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND);
        }
        String orderNo = orderPriceInfoList.get(0).getOrderNo();
        priceInfoService.deleteByUid(uid);
        return dropOffCarDiscount(orderNo, request, adminId);
    }

    /**
     * 還車共用產生折扣刪除
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void deleteDropOffCarDiscount(Integer id) {
        OrderPriceInfo info = priceInfoService.get(id);
        if (info.getReceivedAmount() != 0) {
            throw new SubscribeException(ORDER_PRICE_INFO_DISCOUNT_PAID_CANNOT_EDIT_DELETE);
        }
        priceInfoService.delete(id);
    }

    /**
     * 還車共用產生折扣刪除
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void deleteDropOffCarDiscountByUid(String uid) {
        List<OrderPriceInfo> infos = priceInfoService.getByUid(uid);
        for (OrderPriceInfo info : infos) {
            if (info.getReceivedAmount() != 0) {
                throw new SubscribeException(ORDER_PRICE_INFO_DISCOUNT_PAID_CANNOT_EDIT_DELETE);
            }
        }
        priceInfoService.deleteByUid(uid);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void orderCloseRequest(AdminUser adminUser, String orderNo, OrderCloseRequest orderCloseRequest) {

        Orders order = this.getOrder(orderNo);
        if (ARRIVE_NO_CLOSE.getStatus() != order.getStatus()) {
            throw new SubscribeException(ORDER_STATUS_NOT_ARRIVE_NO_CLOSE);
        }

        AccidentInfo accidentInfo = order.getAccidentInfo();
        if (accidentInfo != null && "Y".equals(accidentInfo.getReturnNego())) {
            throw new SubscribeException(ACCIDENT_CANNOT_CLOSE);
        }

        accidentInfo.setAdminId(adminUser.getMemberId());
        accidentInfo.setManagerId(orderCloseRequest.getManagerId());
        order.setAccidentInfo(accidentInfo);
        orderRepository.save(order);
        notifyService.notifyReturnNotCloseRequest(order, adminUser);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void orderCloseAgree(String headerMemberId, String orderNo, OrderCloseAgreeRequest orderCloseAgreeRequest) {

        Orders order = this.getOrder(orderNo);
        Contract contract = order.getContract();
        MainContract mainContract = contract.getMainContract();

        AccidentInfo accidentInfo = order.getAccidentInfo();
        if (accidentInfo != null) {
            if ("Y".equals(accidentInfo.getReturnNego())) {
                throw new SubscribeException(ACCIDENT_CANNOT_CLOSE);
            } else {
                accidentInfo.setAgree(true);
                accidentInfo.setManagerId(headerMemberId);
                accidentInfo.setCloseRemark(orderCloseAgreeRequest.getCloseRemark());
                accidentInfo.setCloseDate(new Date());
            }
        }
        if (order.getRenewType() != RenewType.RENEW) {
            mainContract.setStatus(ContractStatus.COMPLETE.getCode());
            contract.setStatus(ContractStatus.COMPLETE.getCode());
        }
        orderRepository.save(order);
        contractService.updateMainContract(mainContract);
        contractService.updateContract(contract);
        // 確認狀態
        OrderStatus orderStatus = OrderStatus.of(order.getStatus());
        if (orderStatus != ARRIVE_NO_CLOSE) {
            throw new BadRequestException(order.getStatus() == CLOSE.getStatus() ? "訂單已結案" : String.format("訂單狀態[%s]不可結案", orderStatus.getName()));
        }
        // 議價結束
        if (order.getAccidentInfo() != null && "Y".equals(order.getAccidentInfo().getReturnNego())) {
            throw new BadRequestException("還車議價中不可結案");
        }
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(orderNo);
        // 付款確認
        if (priceInfoWrapper.excludeSecurityDeposit().getUnpaid().getActualPrice() != 0) {
            throw new BadRequestException("付款/退款未完成!");
        }
        InvoiceInfo invoiceInfo = new InvoiceInfo(invoiceServiceV2.getInvoice(order.getOrderNo()));
        // 發票開立金額
        if (priceInfoWrapper.getCurrentReceivable().getActualPrice() != invoiceInfo.getTotalAmount()) {
            throw new BadRequestException("發票開立金額不等於付款金額!");
        }
        if (ARRIVE_NO_CLOSE.getStatus() == order.getStatus()) {
            order.setStatus(CLOSE.getStatus());
            if (!Objects.equals(order.getRenewType(), RenewType.RENEW) && !Objects.equals(RenewType.AUTO_RENEW, order.getRenewType())) {
                refundSecurityDeposit(mainContract);
            }
            orderRepository.save(order);

            eventPublisher.publishEvent(new OrderClosedEvent(this, order, orderCloseAgreeRequest.getCloseRemark(), headerMemberId));
        }
    }

    /**
     * 提前還車 檢視
     * 已使用天數 < 三個月 計算提前還車退款
     * 已使用天數 >= 三個月 計算提前解約罰金
     */
    public ReturnEarlyCalculateResponse dropOffCarEarlyView(String orderNo) {
        ReturnEarlyCalculateResponse response = new ReturnEarlyCalculateResponse();
        Orders order = this.getOrder(orderNo);
        Contract contract = order.getContract();
        MainContract mainContract = contract.getMainContract();
        int monthlyFee = mainContract.getOriginalPriceInfo().getUseMonthlyFee();
        int insuranceFee = Optional.ofNullable(order.getContract()).map(Contract::getDisclaimer).map(disClaimer -> disClaimer ? order.getContract().getMainContract().getOriginalPriceInfo().getDisclaimerFee() : 0).orElse(0);

        // 已使用天數
        int usedDays = (int) DateUtil.calculateDiffDate(order.getStartDate(), order.getEndDate(), DAYS);
        response.setUseDays(usedDays);

        if (usedDays < 92) {
            // 退款天數
            int contractDays = (int) DateUtil.calculateDiffDate(order.getStartDate(), order.getExpectEndDate(), DAYS);
            // 預估應退金額
            long refund = (contractDays - usedDays) * Math.round((monthlyFee + insuranceFee) * order.getMonth() * 1.0 / contractDays);
            response.setRefund((int) refund);
        } else {
            // 預估應繳罰款
            List<OrderPriceInfo> unpaidList = orderPriceInfoRepository.findAll((Specification<OrderPriceInfo>) (root, query, builder) -> {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.type), Pay.getCode()));
                predicates.add(builder.notEqual(root.get(OrderPriceInfo.Fields.amount), 0));
                predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.receivedAmount), 0));
                return builder.and(predicates.toArray(new Predicate[0]));
            });

            Integer fine = unpaidList.stream().map(OrderPriceInfo::getAmount).reduce(0, Integer::sum);
            response.setFine(fine);
        }
        return response;
    }

    /**
     * 設定車損費用(By Contract)
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void setAccidentInfoByContract(String memberId, String contractNo, InternalAccidentRequest accidentRequest) {
        Contract contract = contractService.getContractAndOrdersByContractNo(contractNo);
        Orders orders = contract.getOrders().stream().filter(order -> DEPART.getStatus() == order.getStatus()).findAny().orElseThrow(() -> new SubscribeException(DEPART_ORDER_NOT_FOUND));
        int latestStage = DateUtil.calculateStageAndDate(orders).size();
        ExtraFeeRequest request = toExtraFeeReq(accidentRequest, latestStage);
        priceInfoService.setExtraFee(orders.getOrderNo(), request, memberId, true);
    }

    /**
     * 設定車損費用
     */
    @Deprecated
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void setAccidentInfo(String orderNo, InternalAccidentRequest request) {
        Orders order = getOrder(orderNo);
        if (order.getStatus() < DEPART.getStatus()) {
            throw new BadRequestException("需出車後才可設定車損");
        }
        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrderAndAcctId(orderNo, order.getContract().getMainContract().getAcctId())
            .stream().filter(orderPriceInfo -> orderPriceInfo.getCategory() == CarAccident).collect(Collectors.toList());

        AccidentInfo info = new AccidentInfo();
        BeanUtils.copyProperties(request, info);
        // 無車損或車損議價中
        if (!info.isCarDamaged() || (info.isCarDamaged() && "Y".equals(info.getReturnNego()))) {
            // 無車損，則全退車損費用
            orderPriceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getType() == Pay.getCode())
                .forEach(orderPriceInfo -> priceInfoService.refundAll(orderPriceInfo.getId(), null));
        } else {
            priceInfoService.calculateAccident(order, request.getCarLossAmt());
        }
        orderPriceInfoRepository.deleteNotPaidEmpDiscardFee(orderNo);
        order.setAccidentInfo(info);
        contractService.updateMainContract(order.getContract().getMainContract());
    }

    public Page<OrderQueryResponse> searchByPage(PageRequest pageRequest, OrdersCriteria queryRequest) {
        int limit = pageRequest.getLimit();
        int offset = pageRequest.getSkip();
        if (StringUtils.isNotBlank(queryRequest.getIdNo()) || StringUtils.isNotBlank(queryRequest.getAcctName()) || StringUtils.isNotBlank(queryRequest.getPhone())) {
            queryRequest.setAcctId(authServer.getUsers(queryRequest.getAcctName(), queryRequest.getPhone(), queryRequest.getIdNo()));
            if (queryRequest.getAcctId() == null || queryRequest.getAcctId().isEmpty()) {
                return Page.of(0, Collections.emptyList(), offset, limit);
            }
        }
        queryRequest.validate();
        long total = orderRepository.count(queryRequest);
        if (0 == total) {
            return Page.of(0, Collections.emptyList(), offset, limit);
        }
        List<OrderQueryResponse> list = searchPage(queryRequest, limit, offset);

        return Page.of(total, list, offset, limit);
    }

    private List<OrderQueryResponse> searchPage(OrdersCriteria queryRequest, Integer limit, Integer offset) {
        Set<Integer> acctIds = new HashSet<>();
        Map<Integer, AuthUser> userMap;
        Map<String, MemberInfo> memberMap = new HashMap<>();
        Map<String, Stations> stationsMap = stationService.findAll().stream().collect(Collectors.toMap(Stations::getStationCode, station -> station));
        Map<Integer, SubscribeLevel> subscribeLevelMap = subscribeLevelService.getAll().stream().collect(Collectors.toMap(SubscribeLevel::getLevel, level -> level));
        List<OrderQueryResponse> responses = orderRepository.findBySearch(queryRequest, limit, offset).stream().map(o -> {

                OrderQueryResponse response = new OrderQueryResponse(o);
                Optional.ofNullable(response.getAcctId()).ifPresent(acctIds::add);
                Optional.ofNullable(stationsMap.get(response.getDepartStation())).map(Stations::getStationName).ifPresent(response::setDepartStationName);
                Optional.ofNullable(stationsMap.get(response.getReturnStation())).map(Stations::getStationName).ifPresent(response::setReturnStationName);
                Optional.ofNullable(o.getMainContract()).ifPresent(mainContract -> {
                    response.setSubscribeLevel(new SubscribeLevelResponse(subscribeLevelMap.get(mainContract.getGivenCarLevel())));
                });
                response.setEContractSigned(econtractService.isSignEContract(o.getContract()));

                return response;
            }
        ).collect(Collectors.toList());
        userMap = authServer.getUserAcctIds(acctIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(AuthUser::getAcctId, user -> user));
        Map<String, Integer> paidMap = null;
        Map<String, Integer> totalMap = null;
        if (queryRequest.isQueryPaid()) {
            List<String> orderNos = responses.stream().map(OrderQueryResponse::getOrderNo).collect(Collectors.toList());
            paidMap = priceInfoService.paidAmt(orderNos, false, true);
            totalMap = priceInfoService.getTotalAmount(orderNos, true);
        }
        Map<String, Integer> finalPaidMap = paidMap;
        Map<String, Integer> finalTotalMap = totalMap;
        responses.forEach(o -> {
            try {
                if (o.getAcctId() != null && userMap.get(o.getAcctId()) != null) {
                    AuthUser user = userMap.get(o.getAcctId());
                    o.setCustName(user.getAcctName());
                    o.setMainCell(user.getMainCell());
                    o.setNationalCode(user.getNationalCode());
                    o.setCustMemo(user.getDescr());
                    o.setCity(user.getCityId());
                    o.setArea(user.getAreaId());
                    o.setAddress(user.getAddr());
                    o.setHhrCityId(user.getHhrCityId());
                    o.setHhrAreaId(user.getHhrAreaId());
                    o.setHhrAddr(user.getHhrAddr());
                }
                if (StringUtils.isNotBlank(o.getDepartMemberId())) {
                    AtomicReference<MemberInfo> memberInfo = new AtomicReference<>(memberMap.get(o.getDepartMemberId()));
                    if (memberInfo.get() == null) {
                        authorityServer.getMemberInfos(o.getDepartMemberId()).stream().findAny().ifPresent(m -> {
                            memberInfo.set(m);
                            memberMap.put(m.getMemberId(), m);
                        });
                    }
                    o.setDepartMemberName(memberInfo.get().getMemberName());
                }
                if (StringUtils.isNotBlank(o.getReturnMemberId())) {
                    AtomicReference<MemberInfo> memberInfo = new AtomicReference<>(memberMap.get(o.getReturnMemberId()));
                    if (memberInfo.get() == null) {
                        authorityServer.getMemberInfos(o.getReturnMemberId()).stream().findAny().ifPresent(m -> {
                            memberInfo.set(m);
                            memberMap.put(m.getMemberId(), m);

                        });
                    }
                    o.setReturnMemberName(memberInfo.get().getMemberName());
                }

                // 處理費用
                if (queryRequest.isQueryPaid()) {
                    o.setPaidAmt(finalPaidMap.get(o.getOrderNo()));
                    o.setTotalAmt(finalTotalMap.get(o.getOrderNo()));
                }
            } catch (Exception e) {
                log.error("查詢使用者{}失敗", o.getAcctId(), e);
            }
        });
        return responses;
    }

    public CsvUtil.ByteArrayOutputStream2ByteBuffer generateOrderCsv(OrdersCriteria queryRequest) {
        // 處理查詢條件
        if (StringUtils.isNotBlank(queryRequest.getIdNo()) || StringUtils.isNotBlank(queryRequest.getAcctName()) || StringUtils.isNotBlank(queryRequest.getPhone())) {
            queryRequest.setAcctId(authServer.getUsers(queryRequest.getAcctName(), queryRequest.getPhone(), queryRequest.getIdNo()));
        }

        // 初始化必要的 Map
        Map<Integer, AuthUser> userMap = new HashMap<>();
        Map<String, MemberInfo> memberMap = new HashMap<>();
        Map<String, String> stationsMap = stationService.findAll().stream().collect(Collectors.toMap(Stations::getStationCode, Stations::getStationName));

        // 查詢訂單資料
        List<OrderDTO> objects = orderRepository.findBySearch(queryRequest, null, null);
        List<String> orderNos = objects.stream().map(object -> Optional.ofNullable(object.getOrders()).map(o -> ((Orders) o).getOrderNo()).orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND)))
            .filter(Objects::nonNull).collect(Collectors.toList());

        // 獲取費用資訊
        Map<String, List<OrderPriceInfo>> priceInfoMap = priceInfoService.getPriceInfosByOrders(orderNos).stream()
            .collect(Collectors.groupingBy(OrderPriceInfo::getOrderNo));

        // 預先取得所有 Sku 資料
        Map<String, Sku> allSkus = skuService.getAllSkus();

        // 預先取得所有車籍公司所屬資料
        Map<String, CarRegistration> carRegistrationMap = carRegistrationService.getAllCarRegistrations().stream()
            .collect(Collectors.toMap(CarRegistration::getVatNo, Function.identity()));

        // 處理訂單資料轉換
        List<OrderCSV> responses = objects.stream()
            .map(result -> {
                Orders orders = (Orders) result.getOrders();
                List<OrderPriceInfo> orderPriceInfos = priceInfoMap.get(orders.getOrderNo());

                OrderCSV csv = new OrderCSV(
                    orders,
                    (MainContract) Optional.ofNullable(result.getMainContract()).orElseGet(MainContract::new),
                    (CarModel) Optional.ofNullable(result.getCarModel()).orElseGet(CarModel::new),
                    (CarBrand) Optional.ofNullable(result.getCarBrand()).orElseGet(CarBrand::new),
                    (Cars) Optional.ofNullable(result.getCar()).orElseGet(Cars::new),
                    objectMapper
                );

                // 設定站點資訊
                csv.setDepartStation(stationsMap.get(csv.getDepartStation()));
                csv.setReturnStation(stationsMap.get(csv.getReturnStation()));

                // 設定費用相關資訊
                if (CollectionUtils.isEmpty(orderPriceInfos)) {
                    // 設定預設值
                    csv.setDiscountMileage(0);
                    csv.setQuarterlyFeeDiscountAmount(0);
                } else {
                    int discountMileage = 0;
                    int quarterlyFeeDiscountAmount = 0;
                    Set<String> skuCodes = new HashSet<>();
                    List<OrderPriceInfo> merchandiseInfos = new ArrayList<>();

                    for (OrderPriceInfo pi : orderPriceInfos) {
                        if (pi != null) {
                            // 計算折扣里程
                            PriceInfoDetail detail = pi.getInfoDetail();
                            if (detail != null) {
                                discountMileage += Optional.ofNullable(detail.getDiscountMileage()).orElse(0);
                            }

                            // 計算季度費用折扣
                            if (pi.getCategory().equals(MonthlyFee) && pi.getType() == Pay.getCode()) {
                                quarterlyFeeDiscountAmount += pi.getAmount();
                            }

                            // 收集商品資訊
                            if (pi.getCategory().equals(Merchandise) && pi.getType() == Pay.getCode()) {
                                skuCodes.add(pi.getSkuCode());
                                merchandiseInfos.add(pi);
                            }
                        }
                    }

                    // 設定折扣里程和季度費用折扣
                    csv.setDiscountMileage(discountMileage);
                    csv.setQuarterlyFeeDiscountAmount(quarterlyFeeDiscountAmount);

                    // 只在有商品時才查詢 Sku
                    if (!skuCodes.isEmpty()) {
                        // 直接從預先載入的 Sku Map 中取得資料,避免多次查詢資料庫
                        String merchandiseDetail = merchandiseInfos.stream()
                            .map(pi -> {
                                Sku sku = allSkus.get(pi.getSkuCode());
                                PriceInfoDetail infoDetail = pi.getInfoDetail();
                                return String.format("%s($%s) x %d",
                                    sku != null ? sku.getName() : "Unknown",
                                    PriceUtils.formatWithThousandsSeparator(Optional.ofNullable(infoDetail.getActualUnitPrice()).orElse(infoDetail.getUnitPrice())),
                                    infoDetail.getQuantity());
                            })
                            .collect(Collectors.joining(";"));

                        csv.setMerchandiseDetail(merchandiseDetail);
                    }
                }

                if (StringUtils.isNotBlank(csv.getVatNo())) {
                    CarRegistration carRegistration = carRegistrationMap.get(csv.getVatNo());
                    if (carRegistration != null) {
                        csv.setCarOwnerName(carRegistration.getShortName());
                    }
                }
                return csv;
            }).collect(Collectors.toList());

        List<City> cities = goSmartServer.getCityArea();
        for (List<Integer> acctIds : Lists.partition(new ArrayList<>(responses.stream().map(OrderCSV::getAcctId).collect(Collectors.toSet())), 200)) {
            userMap.putAll(authServer.getUserAcctIds(acctIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(AuthUser::getAcctId, Function.identity())));
        }
        responses.forEach(o -> {
            try {
                if (o.getAcctId() != null) {
                    AuthUser user = userMap.get(o.getAcctId());
                    o.setCustName(user.getAcctName());
                    o.setMainCell(String.format("%s %s", user.getNationalCode(), user.getMainCell()));
                    o.setIdNo(user.getLoginId());
                    o.setJurisdictionNum(user.getJurisdictionNum());
                    o.setBirthDay(user.getBirthday());
                    o.setCity(user.getHhrCityId());
                    o.setArea(user.getHhrAreaId());
                    o.setAddress(user.getHhrAddr());
                }
            } catch (Exception e) {
                log.error("查詢使用者{}失敗", o.getAcctId(), e);
            }

            // 設置出車人員資訊
            if (StringUtils.isNotBlank(o.getDepartMemberId())) {
                AtomicReference<MemberInfo> memberInfo = new AtomicReference<>(memberMap.get(o.getDepartMemberId()));
                if (memberInfo.get() == null) {
                    authorityServer.getMemberInfos(o.getDepartMemberId()).stream().findAny().ifPresent(m -> {
                        memberInfo.set(m);
                        memberMap.put(m.getMemberId(), m);
                    });
                }
                o.setDepartMemberName(Optional.ofNullable(memberInfo.get()).map(MemberInfo::getMemberName).orElse(o.getDepartMemberId()));
            }
            // 設置還車人員資訊
            if (StringUtils.isNotBlank(o.getReturnMemberId())) {
                AtomicReference<MemberInfo> memberInfo = new AtomicReference<>(memberMap.get(o.getReturnMemberId()));
                if (memberInfo.get() == null) {
                    authorityServer.getMemberInfos(o.getReturnMemberId()).stream().findAny().ifPresent(m -> {
                        memberInfo.set(m);
                        memberMap.put(m.getMemberId(), m);

                    });
                }
                o.setReturnMemberName(Optional.ofNullable(memberInfo.get()).map(MemberInfo::getMemberName).orElse(o.getReturnMemberId()));
            }
            setCityArea(o, cities);
        });

        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            responses,
            new String[] {"主約編號", "主約狀態", "合約編號", "備車方式", "訂單編號", "是否新單", "訂單狀態", "訂車人", "出車站點",
                "還車站點", "訂單建立時間", "訂單成立時間", "預定出車時間", "實際出車時間", "預定還車時間", "實際還車時間",
                "法人統一編號", "法人公司名稱", "法人公司地址", "發票對象", "發票統編", "發票抬頭",
                "utmCampaign", "utmMedium", "utmSource", "介紹人", "保證金", "實際月費", "里程費率", "是否啟用優惠月費",
                "車牌號碼", "廠牌", "車型", "車輛編號", "排氣量", "車色", "排檔方式", "燃料種類", "出廠年份", "首期預付月費",
                "首期金額合計", "身分證號/居留證號", "駕照管轄編號", "出生年月日", "連絡電話", "戶籍縣市",
                "戶籍區域", "戶籍地址", "訂單備註", "不續約原因", "出車人員編號", "出車人員", "還車人員編號", "還車人員",
                "總預付月費", "實際使用里程數", "優惠里程數", "每季月費優惠折抵金額", "加購商品明細", "車輛統一編號", "車輛所屬公司名稱"},
            true,
            ',',
            out,
            Charset.forName("big5"),
            OrderCSV.class
        );
        return out;
    }

    private void setCityArea(OrderCSV order, List<City> cities) {
        if (order.getCity() != null && order.getArea() != null) {
            City city = cities.stream().filter(c -> c.getCityId() == order.getCity()).findAny().orElse(null);
            if (city != null) {
                order.setCityName(city.getCityName());
                if (city.getArea() != null) {
                    Optional.ofNullable(city.getArea()).orElse(Collections.emptyList()).stream()
                        .filter(area -> area.getAreaId() == order.getArea())
                        .findAny().map(Area::getAreaName).ifPresent(order::setAreaName);
                }
            }
        }
    }


    /**
     * 紀錄續約狀態到上期訂單
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void recordRenewTypeToPreOrder(String lastOrderNo, RenewType renewType) {
        Orders preOrder = getPreviousOrders(lastOrderNo);
        if (preOrder == null) {
            Orders orders = getOrder(lastOrderNo);
            preOrder = contractLogic.getDepartOrdersByMainContract(orders.getContract().getMainContract().getMainContractNo());
        }
        if (Objects.nonNull(preOrder)) {
            preOrder.setRenewType(renewType);
            if (RenewType.RENEW == renewType || Objects.equals(RenewType.AUTO_RENEW, renewType)) {
                preOrder.setNextStageOrderNo(lastOrderNo);
            } else if (RenewType.CANCEL == renewType || RenewType.PENDING == renewType) {
                preOrder.setNextStageOrderNo(null);
            }
            orderRepository.save(preOrder);
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateRenewStatus(String orderNo, int acctId, OrderRenewStatusRequest request) {
        Orders orders = getUserOrder(orderNo, acctId);
        updateRenewStatus(orders, request);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateRenewStatus(String orderNo, OrderRenewStatusRequest request) {
        Orders orders = getOrder(orderNo);
        updateRenewStatus(orders, request);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateRenewStatus(Orders order, OrderRenewStatusRequest request) {
        if (BOOKING.getStatus() != order.getStatus() && DEPART.getStatus() != order.getStatus()) {
            throw new SubscribeException(ORDER_STATUS_NOT_BOOKING_NOT_DEPART);
        }
        if (RenewType.RENEW == request.getRenewType()) {
            if (DEPART.getStatus() != order.getStatus()) {
                throw new SubscribeException(ORDER_STATUS_NOT_DEPART);
            }
        }

        order.setRenewType(request.getRenewType());
        if (RenewType.CANCEL == request.getRenewType()) {
            order.setNonRenewRemark(request.getNonRenewRemark());
        } else {
            order.setNonRenewRemark("");
        }
        orderRepository.save(order);

        // 確認不續約通知
        if (RenewType.CANCEL == request.getRenewType()) {
            AuthUser user = authServer.getUserWithRetry(order.getContract().getMainContract().getAcctId());
            notifyService.notifyNotRenewConfirm(order, user);
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public List<Orders> getOrdersByContractNo(String contractNo) {
        return orderRepository.findAll((Specification<Orders>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(Orders.Fields.contractNo), contractNo));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
    }

    /**
     * 設定里程折扣
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateMileageDiscounts(String orderNo, MileageDiscountRequest mileageDiscountRequest) {
        Orders order = getOrder(orderNo);
        updateMileageDiscounts(order, mileageDiscountRequest);
    }

    /**
     * 設定里程折扣
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateMileageDiscounts(Orders order, MileageDiscountRequest mileageDiscountRequest) {
        if (mileageDiscountRequest.getMileageDiscounts() != null && !mileageDiscountRequest.getMileageDiscounts().isEmpty()) {
            List<OrderPriceInfo> mileageLists = priceInfoService.discountMileageFee(order, mileageDiscountRequest.getMileageDiscounts());
            mileageLists = orderPriceInfoRepository.saveAll(mileageLists);
            for (OrderPriceInfo orderPriceInfo : mileageLists) {
                EmpMileageDiscount empMileageDiscount = mileageDiscountRequest.getMileageDiscounts().get(orderPriceInfo.getStage());
                if (empMileageDiscount != null) {
                    Integer endMileage = orderPriceInfo.getInfoDetail().getEndMileage();
                    if (endMileage != null) {
                        priceInfoService.calculateMillageFee(order.getOrderNo(), order.getContract().getMainContract().getAcctId(), endMileage, orderPriceInfo.getId());
                    }
                }
            }
        }
    }

    /**
     * 拿取指定訂單狀態的訂單清單
     */
    public List<Orders> getOrdersByStatus(OrderStatus orderStatus) {
        return orderRepository.getOrderByStatus(orderStatus);
    }

    /**
     * 透過訂單取消鎖車
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void releaseCar(String orderNo, int acctId) {
        Orders orders = getUserOrder(orderNo, acctId);
        Cars car = carsService.findByPlateNo(orders.getPlateNo());
        if (orders.getIsNewOrder() && car != null && CarDefine.CarStatus.Subscribed.getCode().equals(car.getCarStatus()) && orders.getStatus() < BOOKING.getStatus()) {
            carsService.updateStatus(car, CarDefine.CarStatus.Free);
        }
    }


    /**
     * 查詢使用者是否有進行中訂單
     */
    public CheckOrdersDoneResponse checkOrdersDone(Integer acctId) {
        OrdersCriteria criteria = new OrdersCriteria();
        criteria.setAcctId(Collections.singletonList(acctId));
        criteria.setStatus(getValidOrderStatuses());

        long total = orderRepository.count(criteria);
        return new CheckOrdersDoneResponse(total == 0);
    }

    /**
     * 紀錄短租轉換到新訂的資訊
     */
    public void recordSrentalInfo(Orders orders) {
        if (!orders.getIsNewOrder()) {
            Orders preOrder = orderRepository.getPreviousOrders(orders.getOrderNo());
            if (preOrder != null) {
                orders.setIsChangeCarRenew(preOrder.getIsChangeCarRenew());
                orders.setSrentalParentOrderNo(preOrder.getSrentalParentOrderNo());
            }
        }
    }

    public OrderDepartRecordResponse findDepartRecord(List<OrderDepartRecordRequest> orderDepartRecordRequests) {
        List<City> cities = goSmartServer.getCityArea();
        OrderDepartRecordResponse response = new OrderDepartRecordResponse();
        response.setCities(cities);
        Map<String, List<OrderDepartRecordRequest>> plateNoParkingRecordMap = orderDepartRecordRequests.stream().collect(Collectors.groupingBy(OrderDepartRecordRequest::getPlateNo));
        OrdersCriteria queryRequest = new OrdersCriteria();
        queryRequest.setStatus(Arrays.asList(DEPART.getStatus(), ARRIVE_NO_CLOSE.getStatus(), STOLEN.getStatus(), CLOSE_WITH_SUB.getStatus(), CLOSE.getStatus()));
        queryRequest.setPlateNo(new ArrayList<>(plateNoParkingRecordMap.keySet()));
        Map<String, List<OrderQueryResponse>> orderResponseMap = searchByPage(new PageRequest(Integer.MAX_VALUE, 0), queryRequest).getList().stream().collect(Collectors.groupingBy(OrderQueryResponse::getPlateNo));
        for (Map.Entry<String, List<OrderDepartRecordRequest>> entry : plateNoParkingRecordMap.entrySet()) {
            List<OrderDepartRecordRequest> parkingRecords = entry.getValue().stream().sorted(Comparator.comparing(OrderDepartRecordRequest::getParkingDate)).collect(Collectors.toList());
            for (OrderDepartRecordRequest parkingRecord : parkingRecords) {
                List<OrderQueryResponse> responseList = orderResponseMap.get(entry.getKey());
                if (responseList == null || responseList.isEmpty()) {
                    continue;
                }
                OrderQueryResponse order = responseList.stream().filter(orderQueryResponse -> orderQueryResponse.getStartDate().isBefore(parkingRecord.getParkingDate().toInstant())
                    && ((orderQueryResponse.getEndDate() == null && orderQueryResponse.getStatus() == DEPART.getStatus())
                    || orderQueryResponse.getEndDate().isAfter(parkingRecord.getParkingDate().toInstant())
                )).findAny().orElse(null);
                if (order != null) {
                    OrderDepartRecordResponse.OrderDepartRecord record = new OrderDepartRecordResponse.OrderDepartRecord(order);
                    response.getOrderDepartRecordList().add(record);
                } else {
                    response.getNotFoundRequestList().add(parkingRecord);
                }
            }

        }
        return response;
    }

    /**
     * 建立長租契約
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders createLrentalContract(LrentalContractRequest request, String memberId) {
        Orders orders = getOrder(request.getOrderNo());
        return createLrentalContract(orders, request, memberId);
    }

    /**
     * 建立長租契約
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders createLrentalContract(Orders orders, LrentalContractRequest request, String memberId) {
        String lrentalContractNo = lrentalContractService.createContract(orders, request, memberId);
        orders.setLrentalContractNo(lrentalContractNo);
        if (orders.getIsNewOrder()) {
            insuranceService.createBatchInsurance(orders, request.getMemo(), Optional.ofNullable(request.getLicenseExpDate()).map(Date::toInstant).orElse(null), memberId, CARPLUS_COMPANY_CODE, lrentalContractNo);
        }

        return orderRepository.save(orders);
    }

    /**
     * 建立長租契約 - 出車中換車
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders createLrentalContract(Orders orders, String memberId,
                                        Cars inCar, CarBaseInfoSearchResponse inCarCrsInfo,
                                        List<String> lrContractReplaceCodes, String lrContractMemo, Date licenseExpDate) {
        log.info("建立訂閱客戶約");

        buChangeService.checkSubscribeCarBuChange(inCarCrsInfo, inCar, orders.getOrderNo());
        carsService.update(inCar);
        PurchaseProjectCarSearchResponse purchaseProjectCarSearchResponse = crsService.searchProjectCar(inCarCrsInfo.getCarNo());
        Double mileageFee = ((MileagePriceInfoWrapper) priceInfoService.getPriceInfoWrapper(orders.getOrderNo()).getByCategoryType(MileageFee, Pay)).getMileage();

        ContractAddReq req = contractAddReqMapper.fromOrders(orders, memberId, inCar, lrContractReplaceCodes, lrContractMemo, mileageFee);

        if (!orders.getIsNewOrder()) {
            req.setContractStartDate(DateUtil.transferADDateToMinguoDate(Optional.ofNullable(orders.getStartDate()).orElse(orders.getExpectStartDate()).plus(1, DAYS)));
        }
        if (purchaseProjectCarSearchResponse != null) {
            req.validateProjectCar(purchaseProjectCarSearchResponse, orders.getExpectEndDate());
        }

        String lrentalContractNo = lrentalServer.addContract(req);
        orders.setLrentalContractNo(lrentalContractNo);
        if (orders.getIsNewOrder()) {
            insuranceService.createBatchInsurance(orders, lrContractMemo, Optional.ofNullable(licenseExpDate).map(Date::toInstant).orElse(null), memberId, CARPLUS_COMPANY_CODE, lrentalContractNo);
        }

        return orderRepository.save(orders);
    }

    /**
     * 建立暫存保險
     */
    public void createBatchInsurance(InsuranceContractRequest insuranceContractRequest, String memberId, String companyId) {
        Orders orders = getOrder(insuranceContractRequest.getOrderNo());
        insuranceService.createBatchInsurance(orders, null, insuranceContractRequest.getLicenseExpDate(), memberId, StringUtils.isNotBlank(companyId) ? companyId : CARPLUS_COMPANY_CODE, insuranceContractRequest.getLrentalContractNo());
    }

    /**
     * 建立部門約
     */
    public Orders generateCenterContract(Orders order, Cars cars, boolean needToAssignNewLrentalContractNo) {
        if (!CarsUtil.isCarPlusCar(cars.getVatNo())) {
            return order;
        }
        log.info("建立訂閱部門約");
        if (!CarsUtil.isCarPlusCar(cars.getVatNo())) {
            return order;
        }
        try {
            CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(cars.getPlateNo());
            long count = getProcessOrdersByPlateNoExcludingOrderNo(cars.getPlateNo(), order.getOrderNo()).stream().filter(orderQueryResponse -> StringUtils.isNotBlank(orderQueryResponse.getLrentalOrderNo())).count();
            count = count + dealerOrderService.getProcessingDealerOrdersByPlateNo(cars.getPlateNo()).stream().filter(orderQueryResponse -> StringUtils.isNotBlank(orderQueryResponse.getLrentalContractNo())).count();
            // 沒有長租契約、虛擬車、有撥車編號、CRS為空、CRS的BUID不為訂閱，則不發動建立部門約
            if (StringUtils.isBlank(order.getLrentalContractNo())
                || !CarDefine.CarStatus.Free.getCode().equals(cars.getCarStatus())
                || cars.isVirtualCar()
                || count > 0
                || carBaseInfoSearchResponse == null
                || !Objects.equals(BuIdEnum.subscribe.getCode(), carBaseInfoSearchResponse.getBuId())) {
                return order;
            }
            ContractAddReq req = new ContractAddReq();
            req.setContractType(ContractEnum.ContractType.add_assign_carCenter);
            req.setPlateNo(cars.getPlateNo());
            req.setContractStartDate(DateUtil.transferADDateToMinguoDate(convertToStartOfInstant(Instant.now().plus(1, DAYS))));
            req.setUserId(configService.getSubscribeConfig().getSubscribeDefaultMemberId());
            req.setBuID(BuIdEnum.subscribe.getCode());
            req.setOrderNo(order.getOrderNo());
            PurchaseProjectCarSearchResponse purchaseProjectCarSearchResponse = crsService.searchProjectCar(carBaseInfoSearchResponse.getCarNo());
            if (purchaseProjectCarSearchResponse != null) {
                req.validateProjectCar(purchaseProjectCarSearchResponse, order.getExpectEndDate());
                if (!req.getIsUseCarAgeResidualValue()) {
                    req.setContractType(ContractEnum.ContractType.add_assign_carCenter_project);
                }
            }
            String daNo = lrentalServer.addContract(req);
            if (needToAssignNewLrentalContractNo) {
                order.setLrentalContractNo(daNo);
            }
            return order;
        } catch (Exception e) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("orderNo", order.getOrderNo());
            map.put("errMsg", e.getMessage());
            mattermostServer.notify("訂單自動建立長租契約展期失敗", map, e);
        }
        return order;
    }

    /**
     * 續約後建立長租契約
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Orders createLrentalContractAfterRenew(String memberId, Orders orders, Orders renewOrder) {
        try {
            log.info("建立訂閱長租契約檢查,原始訂單編號{},續約訂單編號{},原始訂單長租編號:{},續約訂單狀態:{},訂單合約編號:{},續約訂單合約編號:{}",
                orders.getOrderNo(), renewOrder.getOrderNo(), orders.getLrentalContractNo(), renewOrder.getStatus(), orders.getContractNo(), renewOrder.getContractNo());
            // 當長租契約不為空 且 狀態>=已訂車 且 是同一合約 則續約自動建立長租契約
            if (StringUtils.isNotBlank(orders.getLrentalContractNo())
                && renewOrder.getStatus() >= BOOKING.getStatus()
                && orders.getContractNo().equals(renewOrder.getContractNo())) {

                // 檢查 memberId 對應的 MemberInfo 的 departmentCode 是否為 訂閱管理課 或 訂閱業務課 之單位代碼
                String effectiveMemberId = Optional.ofNullable(memberId)
                    .filter(StringUtils::isNotBlank)
                    .flatMap(id -> authorityServer.getMemberInfos(id).stream().findFirst()
                        .filter(info -> CarPlusConstant.SUBSCRIBE_DEPT_CODES.contains(info.getDepartmentCode())))
                    .map(MemberInfo::getMemberId)
                    .orElse(configService.getSubscribeConfig().getSubscribeDefaultMemberId());

                LrentalContractRequest request = new LrentalContractRequest();
                request.setOrderNo(renewOrder.getOrderNo());
                ContractSearchRep contractSearchRep = lrentalServer.getContractInfo(orders.getLrentalContractNo());
                List<String> replaceCode = new ArrayList<>();
                for (char c : Optional.ofNullable(contractSearchRep).map(ContractSearchRep::getDachang).orElse("0").toCharArray()) {
                    replaceCode.add(String.valueOf(c));
                }
                request.setReplaceCodes(replaceCode);
                request.setMemo("續約");
                return createLrentalContract(renewOrder, request, effectiveMemberId);
            }
        } catch (Exception e) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("orderNo", renewOrder.getOrderNo());
            map.put("errMsg", e.getMessage());
            mattermostServer.notify("訂單自動建立續約合約失敗", map, e);
        }
        return orders;
    }

    public void addRemark(String orderNo, String content, String memberId) {
        Orders orders = getOrder(orderNo);
        addRemark(orders, content, memberId);
    }

    public void addRemark(Orders order, String content, @Nullable String memberId) {
        if (StringUtils.isBlank(content)) {
            return;
        }
        MemberInfo memberInfo = null;
        if (memberId != null) {
            memberInfo = authorityServer.getMemberInfos(memberId).stream().findFirst()
                .orElseThrow(() -> new SubscribeException(MEMBER_INFO_NOT_FOUND));
        }
        addRemark(order, content, memberInfo);
    }

    public void addRemark(Orders order, String content, @Nullable MemberInfo memberInfo) {
        if (StringUtils.isBlank(content)) {
            return;
        }
        addRemark(order, buildRemark(content, memberInfo));
    }

    public void addRemark(Orders order, Remark remark) {
        appendRemark(order, remark);
        orderRepository.save(order);
    }

    private void appendRemark(Orders order, Remark remark) {
        if (CollectionUtils.isEmpty(order.getRemarks())) {
            order.setRemarks(new ArrayList<>());
        }
        order.getRemarks().add(remark);
    }

    private void appendRemark(Orders order, String content, @NonNull String memberId) {
        if (StringUtils.isBlank(content)) {
            return;
        }
        MemberInfo memberInfo = authorityServer.getMemberInfos(memberId).stream().findFirst()
            .orElseThrow(() -> new SubscribeException(MEMBER_INFO_NOT_FOUND));
        Remark remark = buildRemark(content, memberInfo);
        appendRemark(order, remark);
    }

    public Remark buildRemark(String content, @Nullable MemberInfo memberInfo) {
        String memberId = Optional.ofNullable(memberInfo).map(MemberInfo::getMemberId).orElse("");
        String memberName = Optional.ofNullable(memberInfo).map(MemberInfo::getMemberName).orElse("sys");
        return buildRemark(content, memberId, memberName);
    }

    private Remark buildRemark(String content, String remarkerId, String remarkerName) {
        return Remark.builder()
            .content(content)
            .remarkerId(remarkerId)
            .remarkerName(remarkerName)
            .createTime(new Date())
            .build();
    }

    public Remark buildCustRemark(String content) {
        return buildRemark(content, "", "客戶");
    }

    public void editRemark(String orderNo, Integer remarkIndex, String content, String memberId) {

        if (StringUtils.isBlank(content)) {
            return;
        }

        Orders orders = getOrder(orderNo);

        List<Remark> remarks = orders.getRemarks();
        if (CollectionUtils.isEmpty(remarks) || remarkIndex < 0 || remarkIndex >= remarks.size()) {
            throw new SubscribeException(ORDER_REMARK_NOT_FOUND);
        }
        MemberInfo memberInfo = authorityServer.getMemberInfos(memberId).stream().findFirst()
            .orElseThrow(() -> new SubscribeException(MEMBER_INFO_NOT_FOUND));
        Remark remarkToEdit = remarks.get(remarkIndex);
        remarkToEdit.setContent(content);
        remarkToEdit.setRemarkerId(memberId);
        remarkToEdit.setRemarkerName(memberInfo.getMemberName());
        remarkToEdit.setUpdateTime(new Date());
        remarks.set(remarkIndex, remarkToEdit);
        orders.setRemarks(remarks);
        orderRepository.save(orders);
    }

    public void deleteRemark(String orderNo, int remarkIndex, String memberId) {

        Orders orders = getOrder(orderNo);
        List<Remark> remarks = orders.getRemarks();
        if (CollectionUtils.isEmpty(remarks) || remarkIndex < 0 || remarkIndex >= remarks.size()) {
            throw new SubscribeException(ORDER_REMARK_NOT_FOUND);
        }
        remarks.remove(remarkIndex);
        orders.setRemarks(remarks);
        orderRepository.save(orders);
    }

    public void msgForCustomer(String orderNo, String content, String memberId) {
        Orders orders = getOrder(orderNo);
        MemberInfo memberInfo = authorityServer.getMemberInfos(memberId).stream().findFirst()
            .orElseThrow(() -> new SubscribeException(MEMBER_INFO_NOT_FOUND));
        msgForCustomer(orders.getContract().getMainContract(), content, memberInfo);
    }

    public void msgForCustomer(MainContract mainContract, String content, MemberInfo memberInfo) {
        MsgForCust msgForCust = new MsgForCust(content, memberInfo.getMemberId(), memberInfo.getMemberName());
        Instant now = Instant.now();
        if (CollectionUtils.isEmpty(mainContract.getMsgForCust())) {
            msgForCust.setInstantCreateDate(now);
        } else {
            msgForCust.setInstantCreateDate(mainContract.getMsgForCust().get(0).getInstantCreateDate());
            msgForCust.setInstantUpdateDate(now);
        }
        mainContract.setMsgForCust(Collections.singletonList(msgForCust));
        mainContractRepository.save(mainContract);
    }

    /**
     * 返回訂單編號 list 符合以下提醒建立車籍契約及投保條件
     * 1. status = 10(已訂車)
     * 2. expectDepartDate = 未來 3 天內
     * 3. lrentalContractNo is null
     * 4. 車輛庫位 ≠ 長租 (CRS)
     */
    public List<String> getOrderNoForRemindContractAndInsurance() {
        List<Orders> orders = orderRepository.getOrderNoForRemindContractAndInsurance();
        // 車輛庫位不等於長租(CRS)
        Map<String, CarBaseInfoQueryResponse> cars = crsService.getCarBaseInfoQueryResponses(orders.stream().map(order -> order.getPlateNo()).collect(Collectors.toList()));
        return orders.stream()
            .filter(order -> Optional.ofNullable(cars.get(order.getPlateNo()))
                .map(car -> !Objects.equals(car.getBuId(), BuIdEnum.lRental.getCode()))
                .orElse(false))
            .map(Orders::getOrderNo)
            .collect(Collectors.toList());
    }

    private List<Integer> getValidOrderStatuses() {
        return Arrays.stream(OrderStatus.values())
            .filter(s -> s != CANCEL && s != ARRIVE_NO_CLOSE && s != CLOSE && s != CREDIT_REJECT)
            .map(OrderStatus::getStatus)
            .collect(Collectors.toList());
    }

    public List<Orders> getProcessingOrdersByPlateNo(String plateNo) {
        OrdersCriteria criteria = new OrdersCriteria();
        criteria.setPlateNo(Collections.singletonList(plateNo));
        criteria.setStatus(getValidOrderStatuses());

        return orderRepository.findBySearch(criteria, Integer.MAX_VALUE, 0)
            .stream()
            .map(o -> o.getOrders())
            .collect(Collectors.toList());
    }

    /**
     * 異動訂單出車里程數 (僅限新訂單、已出車未還車且第一筆里程費未付款)
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateDepartMileage(String orderNo, UpdateDepartMileageRequest request, String memberId) {
        Orders order = getOrder(orderNo);
        // 收集所有驗證錯誤
        List<String> validationErrors = new ArrayList<>();
        OrderPriceInfo firstStageMileageFee = null;

        // 檢查是否為新訂單
        if (!order.getIsNewOrder()) {
            validationErrors.add(NOT_NEW_ORDER_CANNOT_UPDATE_DEPART_MILEAGE.getMsg());
        }

        // 檢查訂單狀態
        if (order.getStatus() != DEPART.getStatus()) {
            validationErrors.add(ORDER_STATUS_NOT_DEPART.getMsg());
        }

        // 檢查里程費用資訊
        try {
            firstStageMileageFee = priceInfoService.getPriceInfoWrapper(orderNo).getByCategoryType(MileageFee, Pay).getList().stream()
                .filter(orderPriceInfo -> orderPriceInfo.getStage() == 1)
                .findFirst()
                .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_FIRST_STAGE_MILEAGE_NOT_FOUND));

            if (firstStageMileageFee.isPaid()) {
                validationErrors.add(ORDER_PRICE_INFO_FIRST_STAGE_MILEAGE_ALREADY_PAID.getMsg());
            }
        } catch (SubscribeException e) {
            validationErrors.add(e.getReason());
        }

        // 如果有任何驗證錯誤，拋出包含所有錯誤訊息的異常
        if (!validationErrors.isEmpty()) {
            throw LogicException.of(CarPlusCode.FORBIDDEN, String.join("；", validationErrors));
        }

        // 更新里程費用相關資訊
        PriceInfoDetail detail = firstStageMileageFee.getInfoDetail();
        Integer newDepartMileage = request.getNewDepartMileage();
        detail.setStartMileage(newDepartMileage);

        // 檢查結算里程並重新計算總里程和費用
        if (detail.getEndMileage() != null) {
            int amount = priceInfoService.calculateAmountAndUpdateMileageDetail(detail);
            firstStageMileageFee.setAmount(amount);
        }
        // 如果已執行過還車資料確認，異動出車里程不應大於還車里程
        if (order.getReturnMileage() != null && newDepartMileage > order.getReturnMileage()) {
            throw new SubscribeException(DEPART_MILEAGE_SHOULD_NOT_GREATER_THAN_RETURN_MILEAGE);
        }

        orderPriceInfoRepository.save(firstStageMileageFee);

        // 更新訂單出車里程
        Integer oldDepartMileage = order.getDepartMileage();
        order.setDepartMileage(newDepartMileage);
        MemberInfo memberInfo = authorityServer.getMemberInfos(memberId).stream().findFirst()
            .orElseThrow(() -> new SubscribeException(MEMBER_INFO_NOT_FOUND));
        addRemark(order, String.format("實際出車里程異動：調整前 %d km；調整後 %d km", oldDepartMileage, newDepartMileage), memberInfo);
        // 更新車籍資料當前里程數
        Cars car = Optional.ofNullable(carsService.findByPlateNo(order.getPlateNo())).orElseThrow(() -> new SubscribeException(CAR_NOT_FOUND));
        car.setCurrentMileage(newDepartMileage);
        carsService.update(car);
        // 給客戶留言
        if (StringUtils.isNotBlank(request.getMsgForCustomer())) {
            msgForCustomer(order.getContract().getMainContract(), request.getMsgForCustomer(), memberInfo);
        }
        // 異動出車任務里程
        rentalTaskService.updateDepartMileage(order.getContract(), newDepartMileage);
    }

    boolean isCarPlusOrder(String orderNo) {
        return orderNo.startsWith("B") || orderNo.startsWith("M");
    }

    public Orders getRentalTaskOrderInfo(String contractNo) {
        Contract contract = contractService.getContractAndOrdersByContractNo(contractNo);
        if (contract == null) {
            throw new SubscribeException(CONTRACT_NOT_FOUND);
        }
        return contract.getOrders().stream().filter(order -> order.getStatus().equals(DEPART.getStatus())).findAny().orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));

    }
}
