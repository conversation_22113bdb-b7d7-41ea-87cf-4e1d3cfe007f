package com.carplus.subscribe.controller.contract;

import carplus.common.model.PageRequest;
import carplus.common.redis.cache.Lock;
import carplus.common.response.CarPlusCode;
import carplus.common.response.CarPlusRestController;
import carplus.common.response.Result;
import carplus.common.response.exception.BadRequestException;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.TaskType;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.CheckOrdersDoneResponse;
import com.carplus.subscribe.model.CompanyDriver;
import com.carplus.subscribe.model.authority.AdminUser;
import com.carplus.subscribe.model.order.*;
import com.carplus.subscribe.model.parking.OrderDepartRecordRequest;
import com.carplus.subscribe.model.parking.OrderDepartRecordResponse;
import com.carplus.subscribe.model.priceinfo.resp.OrderPriceInfoResponse;
import com.carplus.subscribe.model.request.contract.*;
import com.carplus.subscribe.model.request.contract.CloseRequest;
import com.carplus.subscribe.model.request.depart.CarDepartFlowRequest;
import com.carplus.subscribe.model.request.depart.CarDepartRequest;
import com.carplus.subscribe.model.request.dropoff.*;
import com.carplus.subscribe.model.request.order.*;
import com.carplus.subscribe.model.request.priceinfo.FineDiscountAgreeRequest;
import com.carplus.subscribe.model.request.priceinfo.MileageDiscountRequest;
import com.carplus.subscribe.model.request.task.DepartTaskRequest;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.response.order.OrderQueryResponse;
import com.carplus.subscribe.service.*;
import com.carplus.subscribe.utils.CsvUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.MULTIPLE_ERRORMESSAGE_EXIST;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 訂閱合約API")
@Slf4j
public class ContractInternalController {

    @Autowired
    private ContractLogic contractLogic;

    @Autowired
    private ContractService contractService;

    @Autowired
    private PriceInfoService priceInfoService;

    @Autowired
    private PaymentServiceV2 paymentService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private RentalTaskService rentalTaskService;

    @Autowired
    private EContractService eContractService;

    @Autowired
    private CheckoutService checkoutService;

    @Operation(summary = "拿取訂單")
    @GetMapping("subscribe/{orderNo}")
    public CashierOrderResponse findOrders(@PathVariable("orderNo") String orderNo) {
        return orderService.getCashierOrder(orderNo);
    }


    @Operation(summary = "建立合約/續約")
    @PostMapping("subscribe/contract")
    public Contract createContract(@Validated @RequestBody InternalContractCreateReq req,
                                   @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {
        contractLogic.contractCreateRequestValidate(req, true);
        return contractLogic.createContract(req, req.getAcctId(), memberId, req.isNeedAutoCredit(), req.getAutoCreditBypassReason());
    }

    @Operation(summary = "續約")
    @PostMapping("subscribe/{orderNo}/renew")
    public Orders renewContract(
        @Validated @RequestBody OrderRenewRequest req,
        @PathVariable("orderNo") String orderNo,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {
        return contractLogic.renewOrder(orderNo, req, true, memberId, req.isNeedAutoCredit(), req.getAutoCreditBypassReason());
    }

    @Operation(summary = "是否可續約")
    @PostMapping("subscribe/{mainContract}/renewable")
    public Boolean renewContract(
        @PathVariable("mainContract") String mainContract) {
        return contractLogic.isRenewable(mainContract);
    }

    @Operation(summary = "續約前計價")
    @PostMapping("subscribe/{orderNo}/calculateRenew")
    public List<OrderPriceInfoResponse> calculateRenewPrice(
        @Validated @RequestBody CalculateOrderRenewRequest req,
        @PathVariable("orderNo") String orderNo) {

        CalculateRequest calculateRequest = contractLogic.prepareRenewalPriceRequest(orderNo, req);
        // 呼叫原有的計價邏輯
        return priceInfoService.calculateOrderPrice(calculateRequest);
    }

    @Operation(summary = "計價")
    @PostMapping("subscribe/calculate")
    public List<OrderPriceInfoResponse> calculatePrice(@Validated @RequestBody CalculateRequest request) {
        InternalContractCreateReq contractCreateReq = new InternalContractCreateReq();
        BeanUtils.copyProperties(request, contractCreateReq, "month");
        contractCreateReq.setMonth(request.getMonth().getValue());
        contractLogic.contractCreateRequestValidate(contractCreateReq, true);
        return priceInfoService.calculateOrderPrice(request);
    }

    @Operation(summary = "更新訂單")
    @PatchMapping(value = "subscribe/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateOrder(
        @Parameter(hidden = true) AdminUser admin,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
        @PathVariable("orderNo") String orderNo,
        @RequestBody @Validated OrderUpdateRequest orderUpdateRequest) {

        orderService.updateOrder(orderNo, orderUpdateRequest, admin, memberId);
    }

    @Operation(summary = "新增訂單備註")
    @PostMapping("subscribe/{orderNo}/remarks")
    public void addOrderRemark(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                               @PathVariable("orderNo") String orderNo,
                               @RequestBody @Validated OrderRemarkRequest orderRemarkRequest) {
        orderService.addRemark(orderNo, orderRemarkRequest.getContent(), memberId);
    }

    @Operation(summary = "編輯訂單備註")
    @PatchMapping("subscribe/{orderNo}/remarks/{remarkIndex}")
    public void editOrderRemark(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                @PathVariable("orderNo") String orderNo,
                                @PathVariable("remarkIndex") Integer remarkIndex,
                                @RequestBody @Validated OrderRemarkRequest orderRemarkRequest) {
        orderService.editRemark(orderNo, remarkIndex, orderRemarkRequest.getContent(), memberId);
    }

    @Operation(summary = "刪除訂單備註")
    @DeleteMapping("subscribe/{orderNo}/remarks/{remarkIndex}")
    public void deleteOrderRemark(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                  @PathVariable("orderNo") String orderNo,
                                  @PathVariable("remarkIndex") @Parameter(description = "註解索引 (第一筆為 0 依序遞增 為動態非固定)") int remarkIndex) {
        orderService.deleteRemark(orderNo, remarkIndex, memberId);
    }

    @Operation(summary = "給客戶留言")
    @PatchMapping("subscribe/{orderNo}/msgForCustomer")
    public void msgForCustomer(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                               @PathVariable("orderNo") String orderNo,
                               @RequestBody @Validated MsgForCustRequest request) {
        orderService.msgForCustomer(orderNo, request.getContent(), memberId);
    }

    @Operation(summary = "撤銷給客戶留言")
    @DeleteMapping("subscribe/{orderNo}/withdrawMsgForCustomer")
    public void withdrawMsgForCustomer(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                       @PathVariable("orderNo") String orderNo) {
        orderService.msgForCustomer(orderNo, null, memberId);
    }

    @Operation(summary = "取消訂單")
    @DeleteMapping("subscribe/{orderNo}")
    public Orders cancelOrder(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                              @PathVariable("orderNo") String orderNo,
                              @Validated @RequestBody CancelRequest req) {
        orderService.processBeforeCancelOrder(orderNo, memberId, req.isForceRemitRefund());
        checkoutService.checkOut(orderNo);
        Orders order = orderService.cancelOrder(orderNo, req, memberId);
        paymentService.refundOrderPriceInfosRetry(order, false);
        return orderService.getOrder(order.getOrderNo());
    }

    @Operation(summary = "法務作業")
    @PatchMapping("subscribe/{orderNo}/legalOperation")
    public void legalOperation(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                               @PathVariable("orderNo") String orderNo,
                               @RequestBody @Validated LegalOperationRequest legalOperationRequest) {
        orderService.legalOperation(orderNo, legalOperationRequest, memberId);
    }

    @Operation(summary = "出車資料確認更新訂單")
    @PatchMapping(value = "subscribe/{orderNo}/depart", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<?> departUpdateOrder(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                       @PathVariable("orderNo") String orderNo,
                                       @RequestBody @Validated CarDepartRequest departRequest) {

        ArrayList<String> result = orderService.departUpdateOrder(orderNo, departRequest, memberId);

        // 若有錯誤訊息
        if (!result.isEmpty()) {
            return Result.of(result, MULTIPLE_ERRORMESSAGE_EXIST, MULTIPLE_ERRORMESSAGE_EXIST.getMsg());
        }
        return Result.success();
    }

    @Operation(summary = "產生出車任務")
    @PostMapping(value = "subscribe/{orderNo}/departTask", produces = MediaType.APPLICATION_JSON_VALUE)
    public void createDepartTask(@PathVariable("orderNo") String orderNo,
                                 @RequestBody @Validated DepartTaskRequest departTaskRequest) {

        orderService.generateDepartTask(orderNo, departTaskRequest);
    }

    @Operation(summary = "出車")
    @PostMapping(value = "subscribe/{orderNo}/depart", produces = MediaType.APPLICATION_JSON_VALUE)
    public void departCar(@PathVariable("orderNo") String orderNo,
                          @RequestBody @Validated CarDepartFlowRequest carDepartFlowRequest) {

        orderService.departCar(orderNo, carDepartFlowRequest);
    }

    @Operation(summary = "出車中換車")
    @PostMapping(value = "subscribe/{orderNo}/{outCarPlateNo}/replace", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<?> replaceCar(@RequestHeader(name = CarPlusConstant.HEADER_COMPANY_CODE) String companyId,
                                @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                @PathVariable("orderNo") String orderNo,
                                @PathVariable("outCarPlateNo") String outCarPlateNo,
                                @RequestBody CarReplaceRequest carReplaceRequest) {
        String messages = orderService.replaceCar(orderNo, carReplaceRequest, companyId, memberId, outCarPlateNo);
        CarPlusCode code = messages.isEmpty() ? CarPlusCode.SUCCESS : SubscribeHttpExceptionCode.BAD_REQUEST;
        messages = messages.isEmpty() ? HttpStatus.OK.getReasonPhrase() : messages;
        return Result.of(null, code, messages);
    }

    @Operation(summary = "出車資料確認更新訂單 by Contract")
    @PatchMapping(value = "subscribe/contract/{contractNo}/depart", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<?> departUpdateOrderByContract(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                                 @PathVariable("contractNo") String econtractRefEntityNo,
                                                 @RequestBody @Validated CarDepartRequest departRequest) {

        ArrayList<String> result = orderService.departUpdateOrderByContract(econtractRefEntityNo, departRequest, memberId);

        // 若有錯誤訊息
        if (!result.isEmpty()) {
            return Result.of(result, MULTIPLE_ERRORMESSAGE_EXIST, MULTIPLE_ERRORMESSAGE_EXIST.getMsg());
        }
        orderService.departCarProcess(econtractRefEntityNo, memberId);
        return Result.success();
    }

    @Operation(summary = "出車 by Contract")
    @PostMapping(value = "subscribe/contract/{contractNo}/depart", produces = MediaType.APPLICATION_JSON_VALUE)
    @Deprecated
    public Result departCarByContract(@PathVariable("contractNo") String contractNo,
                                      @RequestBody @Validated CarDepartFlowRequest departRequest) {
        orderService.departCarByContract(contractNo, departRequest);
        return Result.success();
    }

    @Operation(summary = "還車資料異動")
    @PatchMapping(value = "subscribe/{orderNo}/return", produces = MediaType.APPLICATION_JSON_VALUE)
    public void returnCarConfirm(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                 @PathVariable("orderNo") String orderNo,
                                 @RequestBody @Validated CarDropOffRequest dropOffRequest) {
        orderService.dropOffCarConfirm(orderNo, dropOffRequest, memberId);
    }

    @Operation(summary = "取消提前還車")
    @PatchMapping(value = "subscribe/{orderNo}/return/cancelReturnEarly", produces = MediaType.APPLICATION_JSON_VALUE)
    public void cancelReturnEarly(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                  @PathVariable("orderNo") String orderNo) {
        orderService.undoDropOffCarConfirm(orderNo);
    }

    @Operation(summary = "產生還車任務")
    @PostMapping(value = "subscribe/{orderNo}/returnTask", produces = MediaType.APPLICATION_JSON_VALUE)
    public void createReturnTask(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                 @PathVariable("orderNo") String orderNo,
                                 @RequestBody CarDropOffCompleteRequest request) {
        orderService.generateReturnTask(memberId, orderNo, request);
    }

    @Lock(group = OrderService.class, key = "#orderNo", ttl = 60 * 2)
    @Operation(summary = "還車")
    @PostMapping(value = "subscribe/{orderNo}/return", produces = MediaType.APPLICATION_JSON_VALUE)
    public void returnCar(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String headerMemberId,
                          @PathVariable("orderNo") String orderNo,
                          @RequestBody @Validated CarDropOffCompleteRequest request) {

        orderService.dropOffCar(headerMemberId, orderNo, request);
    }

    @Operation(summary = "還車資料異動 by Contract")
    @PatchMapping(value = "subscribe/contract/{contractNo}/return", produces = MediaType.APPLICATION_JSON_VALUE)
    public void returnCarConfirmByContract(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                           @PathVariable("contractNo") String econtractRefEntityNo,
                                           @RequestBody @Validated CarDropOffRequest dropOffRequest) {
        orderService.dropOffCarConfirmByContract(econtractRefEntityNo, dropOffRequest, memberId);
    }

    @Operation(summary = "延後還車檢視")
    @GetMapping(value = "subscribe/{orderNo}/returnLately", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReturnLateCalculateResponse returnLatelyView(HttpServletRequest request,
                                                        @PathVariable("orderNo") String orderNo) {

        return orderService.dropOffCarLatelyView(orderNo);
    }

    @Operation(summary = "提前還車檢視")
    @GetMapping(value = "subscribe/{orderNo}/returnEarly", produces = MediaType.APPLICATION_JSON_VALUE)
    public ReturnEarlyCalculateResponse returnEarlyView(HttpServletRequest request,
                                                        @PathVariable("orderNo") String orderNo) {

        return orderService.dropOffCarEarlyView(orderNo);
    }

    @Operation(summary = "取消已還車")
    @DeleteMapping(value = "subscribe/{orderNo}/return", produces = MediaType.APPLICATION_JSON_VALUE)
    public Orders cancelReturnCar(@Parameter(hidden = true) AdminUser admin,
                                  @PathVariable("orderNo") String orderNo) {

        return orderService.arriveStatusToArriveNoClose(orderNo, admin);
    }

    @Operation(summary = "受信審核失敗轉審核中")
    @DeleteMapping(value = "subscribe/{orderNo}/credit", produces = MediaType.APPLICATION_JSON_VALUE)
    public Orders cancelCreditFail(@Parameter(hidden = true) AdminUser admin,
                                   @PathVariable("orderNo") String orderNo) {
        return orderService.creditFailToPending(orderNo, admin);
    }

    @Operation(summary = "還車共用產生折扣 且寄信通知主管")
    @PostMapping(value = "subscribe/{orderNo}/returnCarDiscount", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfo> dropOffCarDiscount(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
        @PathVariable("orderNo") String orderNo,
        @RequestBody CarDropOffDiscountRequest dropOffRequest) {
        return orderService.dropOffCarDiscount(orderNo, dropOffRequest, memberId);
    }

    @Operation(summary = "還車共用產生折扣修改 且寄信通知主管")
    @PatchMapping(value = "subscribe/{uid}/returnCarDiscount", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfo> dropOffCarDiscount(HttpServletRequest request,
                                                   @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                                   @PathVariable("uid") String uid,
                                                   @RequestBody CarDropOffDiscountRequest dropOffRequest) {

        return orderService.updateDropOffCarDiscount(uid, dropOffRequest, memberId);
    }

    @Operation(summary = "還車共用產生多筆折扣 且寄信通知主管")
    @PostMapping(value = "subscribe/{orderNo}/returnCarDiscounts", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfo> dropOffCarDiscounts(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
        @PathVariable("orderNo") String orderNo,
        @RequestBody @Validated List<CarDropOffDiscountRequest> dropOffRequests) {
        return orderService.dropOffCarDiscounts(orderNo, dropOffRequests, memberId);
    }

    @Operation(summary = "透過UID取得折扣資訊")
    @GetMapping(value = "subscribe/{uid}/discountInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfo> discountInfo(HttpServletRequest request,
                                             @PathVariable("uid") String uid
    ) {
        return priceInfoService.getByUid(uid);
    }

    @Operation(summary = "還車共用產生折扣刪除")
    @DeleteMapping(value = "subscribe/{priceInfoId}/returnCarDiscount", produces = MediaType.APPLICATION_JSON_VALUE)
    public void CancelDropOffCarDiscount(@PathVariable("priceInfoId") Integer priceInfoId) {
        orderService.deleteDropOffCarDiscount(priceInfoId);
    }

    @Operation(summary = "還車共用產生折扣刪除 (By Uid)")
    @DeleteMapping(value = "subscribe/{uid}/uid/returnCarDiscount", produces = MediaType.APPLICATION_JSON_VALUE)
    public void CancelDropOffCarDiscountByUid(@PathVariable("uid") String uid) {
        orderService.deleteDropOffCarDiscountByUid(uid);
    }


    @Operation(summary = "主管審核請求折扣")
    @PatchMapping(value = "subscribe/{uid}/decide", produces = MediaType.APPLICATION_JSON_VALUE)
    public void decideDiscountEtag(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String adminId,
                                   @PathVariable("uid") String uid,
                                   @RequestBody @Validated FineDiscountAgreeRequest request) {
        priceInfoService.decideDiscount(uid, adminId, request);
    }


    @Operation(summary = "人工授信通過")
    @PostMapping(value = "subscribe/{orderNo}/credit/approve", produces = MediaType.APPLICATION_JSON_VALUE)
    public Orders approveCredit(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                @PathVariable("orderNo") String orderNo,
                                @RequestBody @Validated CreditApprovalRequest request) {
        return orderService.approveCredit(orderNo, memberId, request);
    }

    @Operation(summary = "人工授信不通過")
    @PostMapping(value = "subscribe/{orderNo}/credit/reject", produces = MediaType.APPLICATION_JSON_VALUE)
    public Orders approveCredit(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                @PathVariable("orderNo") String orderNo,
                                @RequestBody @Validated CloseRequest closeRequest) {
        return orderService.rejectCredit(closeRequest, orderNo, memberId);
    }

    @Operation(summary = "重新自動授信")
    @GetMapping(value = "subscribe/{orderNo}/credit/auto", produces = MediaType.APPLICATION_JSON_VALUE)
    public Orders retryAutoCredit(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String headerMemberId,
                                  @PathVariable("orderNo") String orderNo) {
        return orderService.retryAutoCredit(orderNo, headerMemberId);
    }

    @Operation(summary = "車損 結案請求")
    @PostMapping(value = "subscribe/{orderNo}/orderClose", produces = MediaType.APPLICATION_JSON_VALUE)
    public void orderClose(HttpServletRequest request,
                           @Parameter(hidden = true) AdminUser adminUser,
                           @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String headerMemberId,
                           @PathVariable("orderNo") String orderNo,
                           @RequestBody @Validated OrderCloseRequest orderCloseRequest) {

        orderService.orderCloseRequest(adminUser, orderNo, orderCloseRequest);
    }

    @Operation(summary = "車損 確認結案")
    @PatchMapping(value = "subscribe/{orderNo}/orderClose", produces = MediaType.APPLICATION_JSON_VALUE)
    public void orderClose(HttpServletRequest request,
                           @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String headerMemberId,
                           @PathVariable("orderNo") String orderNo,
                           @RequestBody @Validated OrderCloseAgreeRequest orderCloseAgreeRequest) {

        orderService.orderCloseAgree(headerMemberId, orderNo, orderCloseAgreeRequest);
    }

    @Operation(summary = "收銀台訂單查詢")
    @GetMapping(value = "subscribe/order/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<OrderQueryResponse> queryOrder(
        @Validated OrdersCriteria queryRequest) {
        return PageResponse.of(orderService.searchByPage(new PageRequest(queryRequest.getLimit(), queryRequest.getSkip()), queryRequest));
    }

    @Operation(summary = "收銀台訂單CSV下載")
    @GetMapping(value = "subscribe/order/csv", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public byte[] orderCsv(HttpServletResponse res,
                           @Validated OrdersCriteria queryRequest) {
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = orderService.generateOrderCsv(queryRequest);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        res.setHeader("Content-Disposition", "attachment; filename=SUB_order_{" + sf.format(new Date()) + "}.csv");
        return ByteBuffer
            .allocate(out.size())
            .put(out.toByteBuffer())
            .array();
    }

    @Operation(summary = "續約狀態")
    @PatchMapping(value = "subscribe/{orderNo}/renewStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    public void renewStatus(
        @PathVariable("orderNo") String orderNo,
        @RequestBody @Validated OrderRenewStatusRequest orderRenewStatusRequest) {
        orderService.updateRenewStatus(orderNo, orderRenewStatusRequest);
    }

    @Operation(summary = "選擇備車方式")
    @PatchMapping(value = "subscribe/mainContract/{mainContractNo}/carReady", produces = MediaType.APPLICATION_JSON_VALUE)
    public void carReady(
        @PathVariable("mainContractNo") String mainContractNo,
        @RequestBody @Validated CarReadyUpdateRequest request) {
        contractService.updateCarReady(mainContractNo, request);
    }

    @Operation(summary = "設定法人承租人資訊")
    @PatchMapping(value = "subscribe/mainContract/{mainContractNo}/companyDriver", produces = MediaType.APPLICATION_JSON_VALUE)
    public void companyDriver(
        @PathVariable("mainContractNo") String mainContractNo,
        @RequestBody @Validated CompanyDriver companyDriver) {
        contractService.updateCompanyDriver(mainContractNo, companyDriver);
    }

    @Operation(summary = "設定里程優惠")
    @PatchMapping(value = "subscribe/{orderNo}/mileageDiscount", produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateMileageDiscount(
        @PathVariable("orderNo") String orderNo,
        @RequestBody @Validated MileageDiscountRequest mileageDiscountRequest) {
        orderService.updateMileageDiscounts(orderNo, mileageDiscountRequest);
    }

    @Operation(summary = "主合約拿取合約與訂單資訊")
    @GetMapping(value = "subscribe/mainContract/{mainContractNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public MainContract getMainContractAndSubInfos(
        @PathVariable("mainContractNo") String mainContractNo) {
        return contractService.getMainContractAndContractAndOrdersByMainContractNoAndSort(mainContractNo);
    }

    @Operation(summary = "主合約拿取合約與訂單資訊")
    @GetMapping(value = "subscribe/v2/mainContract/{mainContractNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public MainContractCashierResponse getMainContractAndSubInfosV2(
        @PathVariable("mainContractNo") String mainContractNo) {
        return contractService.getCashierMainContract(mainContractNo);
    }

    @Operation(summary = "依車牌&時間找營業/非營業訂單")
    @PostMapping(value = "subscribe/v1/report/orderDepartRecord")
    public OrderDepartRecordResponse getOrderDepartRecord(@RequestBody List<OrderDepartRecordRequest> orderDepartRecordRequests) {
        return orderService.findDepartRecord(orderDepartRecordRequests);
    }

    @Operation(summary = "建立長租契約")
    @PostMapping(value = "subscribe/v1/addLrentalContract")
    public Orders addLrentalContract(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                     @RequestBody LrentalContractRequest lrentalContractRequest) {
        return orderService.createLrentalContract(lrentalContractRequest, memberId);
    }

    @Operation(summary = "建立保險")
    @PostMapping(value = "subscribe/v1/createBatchInsurance")
    public void addLrentalContract(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                   @RequestHeader(name = CarPlusConstant.HEADER_COMPANY_CODE) String companyId,
                                   @RequestBody InsuranceContractRequest insuranceContractRequest) {
        orderService.createBatchInsurance(insuranceContractRequest, memberId, companyId);
    }


    @Operation(summary = "拿取前約訂單")
    @GetMapping("subscribe/preOrder/{orderNo}")
    public Orders findPreOrders(@PathVariable("orderNo") String orderNo) {
        return orderService.getPreviousOrders(orderNo);
    }


    @Operation(summary = "檢查是否有未完成/付款的訂單")
    @GetMapping(value = "subscribe/v1/user/checkOrdersDone", produces = MediaType.APPLICATION_JSON_VALUE)
    public CheckOrdersDoneResponse checkOrdersDone(@RequestParam("acctId") int acctId) {
        return orderService.checkOrdersDone(acctId);
    }

    @Operation(summary = "透過訂單增加da71")
    @GetMapping(value = "subscribe/v1/user/da71/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void checkOrdersDone(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                @PathVariable("orderNo") String orderNo) {
        orderService.addDa71ByOrder(orderNo, memberId);
    }

    @Operation(summary = "異動訂單長租契約編號")
    @PatchMapping(value = "subscribe/v1/user/lrentalContract/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateLRentalContract(
        @PathVariable("orderNo") String orderNo,
        @RequestBody LRentalContractUpdateRequest request) {
        orderService.lrentalContract(orderNo, request);
    }

    @Operation(summary = "電子出租單PDF下載")
    @GetMapping(value = "subscribe/task/rentalPDF/{contractNo}", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public ResponseEntity rentalPDF(@PathVariable("contractNo") String econtractRefEntityNo) {
        try {
            byte[] arr = rentalTaskService.generatePdf(econtractRefEntityNo);
            InputStreamResource streamResource = new InputStreamResource(new ByteArrayInputStream(arr));
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + econtractRefEntityNo + ".pdf")
                .contentType(MediaType.parseMediaType("application/pdf"))
                .body(streamResource);
        } catch (Exception e) {
            log.error("產生電子出租單PDF失敗:", e);
            throw new BadRequestException("產生電子出租單PDF失敗");
        }
    }

    @Operation(summary = "重新產生電子出租單並寄送")
    @PatchMapping(value = "subscribe/task/sendRentalPDF/{contractNo}/{type}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void sendRentalPDF(
        @PathVariable("contractNo") String econtractRefEntityNo,
        @PathVariable("type") TaskType type,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {
        eContractService.reSendAndReGenRentalTaskPdfAndUpLoad(econtractRefEntityNo, memberId, type);
    }

    @Operation(summary = "異動訂單出車里程數 (僅限新訂單、已出車未還車且第一筆里程費未付款)")
    @PatchMapping("subscribe/{orderNo}/departMileage")
    public void updateDepartMileage(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                    @PathVariable("orderNo") String orderNo,
                                    @RequestBody @Validated UpdateDepartMileageRequest updateDepartMileageRequest) {
        orderService.updateDepartMileage(orderNo, updateDepartMileageRequest, memberId);
    }

    @Operation(summary = "透過合約編號取得進行中訂單資訊(電子出租單用)")
    @GetMapping("subscribe/order/task/{contractNo}")
    public Orders getDepartOrderByContractNo(@PathVariable("contractNo") String contractNo) {
        return orderService.getRentalTaskOrderInfo(contractNo);
    }

}
