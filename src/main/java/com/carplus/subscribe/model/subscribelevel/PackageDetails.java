package com.carplus.subscribe.model.subscribelevel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class PackageDetails {
    @Schema(description = "訂閱廠牌")
    private String carBrand;
    @Schema(description = "訂閱車型")
    private String carModel;
    @Schema(description = "訂閱租期")
    private Integer subscribeMonths;
    @Schema(description = "保證金")
    private Integer securityDeposit;
    @Schema(description = "保險加項 - 免責險")
    private Integer disclaimerInsurance;
    @Schema(description = "保險加項 - 溢價險")
    private Integer premiumInsurance;
    @Schema(description = "基本月費")
    private Integer monthlyBaseFee;
    @Schema(description = "每月租金")
    private Integer monthlyTotal;
    @Schema(description = "里程租金")
    private Double mileageFee;
    @Schema(description = "優惠方案")
    private List<String> discounts;
}
