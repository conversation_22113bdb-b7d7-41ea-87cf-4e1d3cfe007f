package com.carplus.subscribe.model.request;

import com.carplus.subscribe.enums.SubscribeType;
import com.carplus.subscribe.model.subscribelevel.MileageDiscount;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.annotations.Type;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

import static com.carplus.subscribe.enums.SubscribeType.SEASON;

@Data
public class SubscribeLevelUpdateRequest {

    @Schema(description = "主鍵")
    @NotNull(message = "主鍵不可為空")
    @Positive(message = "主鍵須大於 0")
    private Integer id;

    @Schema(description = "訂閱車方案")
    @NotNull
    private int level;

    @Schema(description = "訂閱車方案名稱")
    private String name;

    @Schema(description = "對照超激優惠方案")
    @Positive(message = "對照優惠方案須大於 0")
    private Integer discountLevel;

    @Schema(description = "保證金")
    @NotNull
    private int securityDeposit;

    @Schema(description = "基本月費")
    @NotNull
    private int monthlyFee;

    @Schema(description = "里程費")
    @NotNull
    private double mileageFee;

    @Schema(description = "是否自動授信")
    @NotNull
    private boolean autoCredit;

    @Schema(description = "續訂折扣")
    @Type(type = "json")
    private List<Double> renewalDiscountRate = Lists.newArrayList();

    @Schema(description = "里程優惠")
    private List<MileageDiscount> mileageDiscount = Lists.newArrayList();

    @Schema(description = "優惠價")
    @NotNull
    private int discountMonthlyFee;

    @Schema(description = "方案類型")
    @NotNull
    @Enumerated(EnumType.STRING)
    private SubscribeType type = SEASON;

    @Schema(description = "免責險(一季)")
    private Integer disclaimerIns;

    @Schema(description = "溢價險(一季)")
    private Integer premiumIns;

    @Schema(description = "免責險與溢價險一起投保(一季)")
    private Integer disclaimerAndPremiumIns;

    public SubscribeLevelUpdateRequest() {
    }
}
